# 数据库视图分组后端程序设计文档（第二阶段）

## 1. 项目概述

### 1.1 第二阶段目标
基于第一阶段生成的 `view_table_map` 字典，实现视图的来源表分组功能：
- **核心目标**：生成 `source_group_list` - 按来源表相同性分组的视图列表
- **输出格式**：每个元素是一个视图名列表（来源表一致的视图组）

### 1.2 第一阶段成果回顾
- ✅ 成功处理 **264个视图**，成功率 **100%**
- ✅ 生成完整的 `view_table_map_20250721_100803.json` 文件
- ✅ 建立了视图到来源表的完整映射关系

### 1.3 第二阶段核心任务
1. **数据加载**：读取第一阶段的 `view_table_map` 文件
2. **来源表聚类**：基于来源表完全相同进行视图分组
3. **生成分组列表**：创建 `source_group_list` 数据结构
4. **结果输出**：保存为标准化JSON格式，供后续分类使用

### 1.4 与最终分类目标的关系
- 第二阶段：生成基础分组数据（`source_group_list`）
- 后续阶段：基于分组数据实现重复/相似视图分类

## 2. 系统架构设计（第二阶段扩展）

### 2.1 新增目录结构
```
ZhongZhenDemo01/
├── main.py                     # 第一阶段主程序（已有）
├── main_stage2.py              # 第二阶段主程序入口（新增）
├── config/                     # 配置模块（已有）
├── core/                       # 核心业务模块（已有）
│   ├── data_loader.py          # 数据加载器（新增）
│   └── view_grouper.py         # 视图分组器（新增）
├── services/                   # 服务层（已有）
│   └── grouping_service.py     # 分组服务（新增）
├── models/                     # 数据模型（已有）
│   └── grouping_model.py       # 分组结果模型（新增）
├── utils/                      # 工具模块（已有）
│   └── group_analyzer.py       # 分组分析工具（新增）
├── output/                     # 输出目录（已有）
│   ├── results/               # 第一阶段结果（已有）
│   └── stage2_results/        # 第二阶段结果（新增）
└── tests/                     # 测试模块（新增）
    └── test_stage2.py         # 第二阶段测试
```

### 2.2 核心模块设计

#### 2.2.1 数据加载器 (core/data_loader.py)
- **功能**: 加载第一阶段的 `view_table_map` 数据
- **职责**: 文件读取、数据验证、格式转换

#### 2.2.2 视图分组器 (core/view_grouper.py)
- **功能**: 实现基于来源表的视图分组算法
- **职责**: 来源表聚类、生成 `source_group_list`

#### 2.2.3 分组服务 (services/grouping_service.py)
- **功能**: 封装分组业务逻辑
- **职责**: 协调各组件、管理分组流程

#### 2.2.4 分组分析工具 (utils/group_analyzer.py)
- **功能**: 提供分组统计和分析功能
- **职责**: 生成统计报告、质量评估

## 3. 详细设计

### 3.1 核心数据结构

#### 3.1.1 来源表分组模型
```python
@dataclass
class SourceTableGroup:
    """来源表分组模型"""
    source_tables: List[str]           # 来源表列表（排序后）
    view_names: List[str]              # 该组中的视图名列表
    table_signature: str               # 表签名（用于分组标识）
    table_count: int                   # 来源表数量
    view_count: int                    # 视图数量

@dataclass
class SourceGroupListResult:
    """来源表分组列表结果模型"""
    source_group_list: List[List[str]] # 核心输出：视图分组列表
    groups_detail: List[SourceTableGroup]  # 详细分组信息
    total_groups: int                  # 总分组数
    total_views: int                   # 总视图数
    single_view_groups: int            # 单视图分组数
    multi_view_groups: int             # 多视图分组数
    created_time: datetime             # 创建时间
    input_file: str                    # 输入文件路径
```

### 3.2 核心算法设计

#### 3.2.1 来源表分组算法
```python
def group_views_by_source_tables(view_table_map: Dict[str, List[str]]) -> List[List[str]]:
    """
    基于来源表完全相同对视图进行分组

    算法步骤：
    1. 遍历 view_table_map，为每个视图生成表签名
    2. 按表签名将视图分组
    3. 生成 source_group_list 格式的输出

    Args:
        view_table_map: 视图到来源表的映射字典

    Returns:
        List[List[str]]: source_group_list - 每个元素是来源表相同的视图列表
    """
```

#### 3.2.2 表签名生成算法
```python
def generate_table_signature(tables: List[str]) -> str:
    """
    生成来源表的唯一签名，用于分组标识

    算法：
    1. 对表名列表进行排序（确保一致性）
    2. 去除重复项
    3. 用 "|" 分隔符连接生成唯一签名

    Args:
        tables: 来源表列表

    Returns:
        str: 表签名字符串，如 "table_a|table_b|table_c"
    """
```

### 3.3 业务流程设计

#### 3.3.1 第二阶段主流程
```
1. 数据加载阶段
   ├── 自动查找最新的 view_table_map_*.json 文件
   ├── 验证数据完整性和格式
   └── 提取 view_table_map 字典

2. 视图分组阶段
   ├── 为每个视图生成来源表签名
   ├── 按表签名将视图分组
   └── 生成 source_group_list 列表

3. 结果统计阶段
   ├── 统计分组数量和分布
   ├── 分析多视图组和单视图组
   └── 生成处理质量报告

4. 结果输出阶段
   ├── 保存 source_group_list.json（核心输出）
   ├── 保存分组统计报告
   └── 记录处理日志
```

#### 3.3.2 数据验证流程
```
输入验证：
├── 检查 view_table_map 文件存在性
├── 验证JSON格式正确性
├── 检查必要字段（view_table_map, metadata）
└── 验证数据类型和结构

处理验证：
├── 检查视图名称唯一性
├── 验证表名格式合法性
├── 统计数据覆盖率
└── 识别空表名或异常数据

输出验证：
├── 验证分组完整性（所有视图都被分组）
├── 检查分组逻辑正确性
├── 统计分组质量指标
└── 生成验证报告
```

## 4. 接口设计

### 4.1 主要类接口

#### 4.1.1 DataLoader
```python
class DataLoader:
    def load_view_table_map(self, file_path: str) -> Dict[str, List[str]]
    def validate_data(self, data: Dict) -> bool
    def get_latest_result_file(self, directory: str) -> str
```

#### 4.1.2 ViewGrouper
```python
class ViewGrouper:
    def group_by_source_tables(self, view_table_map: Dict) -> List[List[str]]
    def generate_table_signature(self, tables: List[str]) -> str
    def create_detailed_groups(self, view_table_map: Dict) -> List[SourceTableGroup]
```

#### 4.1.3 GroupingService
```python
class GroupingService:
    def run_grouping(self, input_file: str) -> SourceGroupListResult
    def analyze_group_statistics(self, groups: List[List[str]]) -> Dict
    def validate_grouping_result(self, result: SourceGroupListResult) -> bool
```

### 4.2 输出格式设计

#### 4.2.1 source_group_list.json
```json
{
  "source_group_list": [
    ["bond_basicinfo", "bond_basicinfo_dw"],
    ["view_name_1", "view_name_3", "view_name_6"],
    ["single_view_name"]
  ],
  "metadata": {
    "total_groups": 150,
    "total_views": 264,
    "multi_view_groups": 45,
    "single_view_groups": 105,
    "largest_group_size": 8,
    "processing_time": "2025-07-21T10:30:00Z",
    "input_file": "view_table_map_20250721_100803.json"
  }
}
```

#### 4.2.2 grouping_statistics.json
```json
{
  "group_size_distribution": {
    "1": 105,
    "2": 30,
    "3": 10,
    "4": 3,
    "5+": 2
  },
  "table_usage_analysis": {
    "most_common_tables": [
      {"table": "dwd_company_basic", "usage_count": 15},
      {"table": "dwd_bond_basic_info", "usage_count": 12}
    ],
    "unique_table_combinations": 150,
    "total_unique_tables": 89
  },
  "grouping_summary": {
    "total_views_processed": 264,
    "total_groups_created": 150,
    "multi_view_groups": 45,
    "single_view_groups": 105,
    "largest_group_size": 8,
    "average_group_size": 1.76
  }
}
```

## 5. 质量保证措施

### 5.1 向后兼容性
- 保留第一阶段的所有模块和代码
- 新增模块采用独立命名空间
- 不修改现有接口和数据格式

### 5.2 专业一致性
- 统一的表签名生成算法
- 标准化的分组规则
- 可重复的分类依据

### 5.3 结构清晰性
- 模块化设计，职责明确
- 标准化的JSON输出格式
- 完整的类型注解和文档

### 5.4 可维护性
- 统一的变量命名规范
- 完整的单元测试覆盖
- 详细的处理日志记录

## 6. 开发计划

### 6.1 第一步：基础框架搭建
- 创建新增模块文件和目录
- 实现数据加载器
- 建立基础测试框架

### 6.2 第二步：核心分组算法实现
- 实现来源表分组算法
- 开发视图分组器
- 创建分组服务

### 6.3 第三步：结果输出和验证
- 实现 source_group_list 输出功能
- 生成分组统计报告
- 完善错误处理和日志

### 6.4 第四步：测试和优化
- 单元测试和集成测试
- 使用真实数据验证
- 性能优化和文档完善

## 7. 风险评估与应对

### 7.1 技术风险
- **数据一致性风险**: 实现严格的数据验证机制
- **算法复杂性风险**: 采用简单可靠的聚类算法
- **性能风险**: 优化大数据量处理

### 7.2 业务风险
- **分组准确性风险**: 建立严格的表签名生成规则
- **数据一致性风险**: 实现完整的数据验证机制
- **扩展性风险**: 设计灵活的架构支持后续分类需求

## 8. 成功标准

### 8.1 功能标准
- 成功加载第一阶段的所有264个视图数据
- 准确生成基于来源表相同性的视图分组
- 输出标准化的 `source_group_list` 格式

### 8.2 质量标准
- 数据处理准确率 100%（所有视图都被正确分组）
- 分组逻辑一致性验证通过
- 完整的分组统计报告和质量指标

### 8.3 性能标准
- 处理264个视图的分组时间 < 10秒
- 内存使用合理，支持更大数据量扩展
- 输出文件格式规范，便于后续分类程序调用

### 8.4 输出标准
- 生成符合要求的 `source_group_list` 数据结构
- 提供详细的分组统计和分析报告
- 确保向后兼容，不影响第一阶段功能
