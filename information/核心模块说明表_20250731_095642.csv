文件名,作用,核心功能,协同模块
__init__.py,模块初始化,导出核心类和函数,全局
data_loader.py,数据加载器,统一的数据加载和预处理接口,"services, utils"
database_connector.py,数据库连接器,数据库连接管理和查询执行,"config, utils"
field_analyzer.py,字段分析器,分析视图字段结构和类型,view_extractor
sql_analyzer.py,SQL分析器,解析和分析SQL语句结构,view_classifier
sql_comparator.py,SQL比较器,比较SQL语句的相似性和差异,view_classifier
sql_generator.py,SQL生成器,生成优化的合并SQL语句,view_merger
view_classifier.py,视图分类器,AI驱动的视图相似性分析和分类,services
view_extractor.py,视图提取器,从数据库中提取视图定义和元数据,database_connector
view_grouper.py,视图分组器,基于来源表的视图分组逻辑,field_analyzer
view_merger.py,视图合并器,生成智能合并策略和优化SQL,sql_generator
