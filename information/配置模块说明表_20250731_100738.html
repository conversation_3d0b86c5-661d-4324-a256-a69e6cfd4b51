<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置模块说明表</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8A2BE2;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #8A2BE2;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        th {
            background-color: #8A2BE2;
            color: white;
            padding: 12px;
            text-align: center;
            font-weight: bold;
        }
        td {
            padding: 12px;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f8f0ff;
        }
        .filename {
            font-family: 'Consolas', 'Monaco', monospace;
            background-color: #f8f8f8;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .json-file {
            color: #8B4513;
            font-style: italic;
        }
        .role {
            color: #9932CC;
            font-weight: 500;
        }
        .function {
            color: #333;
            line-height: 1.4;
        }
        .scenario {
            color: #666;
            font-style: italic;
            font-size: 13px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>配置模块说明表 (config/)</h1>
        <table>
            <thead>
                <tr>
                    <th style="width: 25%;">文件名</th>
                    <th style="width: 15%;">作用</th>
                    <th style="width: 35%;">核心功能</th>
                    <th style="width: 25%;">使用场景</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="filename ">__init__.py</span></td>
                    <td class="role">模块初始化</td>
                    <td class="function">配置类导出</td>
                    <td class="scenario">全局</td>
                </tr>
                <tr>
                    <td><span class="filename ">api_config.py</span></td>
                    <td class="role">API配置管理</td>
                    <td class="function">DeepSeek API相关配置管理</td>
                    <td class="scenario">services</td>
                </tr>
                <tr>
                    <td><span class="filename ">complete_config.py</span></td>
                    <td class="role">完整配置器</td>
                    <td class="function">高级配置选项和完整验证</td>
                    <td class="scenario">高级用户</td>
                </tr>
                <tr>
                    <td><span class="filename ">database.py</span></td>
                    <td class="role">数据库配置</td>
                    <td class="function">数据库连接参数管理</td>
                    <td class="scenario">core, services</td>
                </tr>
                <tr>
                    <td><span class="filename ">interactive_config.py</span></td>
                    <td class="role">交互配置器</td>
                    <td class="function">标准的交互式配置流程</td>
                    <td class="scenario">普通用户</td>
                </tr>
                <tr>
                    <td><span class="filename ">simple_interactive_config.py</span></td>
                    <td class="role">简化配置器</td>
                    <td class="function">用户友好的快速配置</td>
                    <td class="scenario">新手用户</td>
                </tr>
                <tr>
                    <td><span class="filename json-file">config_template.json</span></td>
                    <td class="role">配置模板</td>
                    <td class="function">标准配置模板和默认值</td>
                    <td class="scenario">初始化</td>
                </tr>
                <tr>
                    <td><span class="filename json-file">user_config.json</span></td>
                    <td class="role">用户配置文件</td>
                    <td class="function">存储用户的个性化设置</td>
                    <td class="scenario">运行时</td>
                </tr>
            </tbody>
        </table>
        <div class="footer">
            <p>生成时间: 2025-07-31 10:07:38</p>
            <p>配置模块说明表 - 数据库视图分析与优化系统</p>
        </div>
    </div>
</body>
</html>