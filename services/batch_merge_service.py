"""
批量合并服务模块
Batch merge service for efficient processing of multiple view groups
"""

import json
import time
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

from services.deepseek_service import DeepSeekService
from models.merge_model import ViewMergeResult, MergeStrategy, FieldMapping
from core.field_analyzer import FieldAnalyzer
from utils.logger import log_info, log_error, log_warning

class BatchMergeService:
    """批量合并服务类"""


    def __init__(self):
        self.deepseek_service = DeepSeekService()
        self.field_analyzer = FieldAnalyzer()
        
        # 批量处理配置
        self.batch_size = 3  # 每批处理的视图组数量
        self.max_workers = 2  # 最大并发线程数
        self.batch_delay = 1.0  # 批次间延迟（秒）
    
    def batch_merge_views(self, strategies: List[MergeStrategy], view_definitions: Dict[str, str]) -> List[ViewMergeResult]:
        """
        批量合并视图
        
        Args:
            strategies: 合并策略列表
            view_definitions: 视图定义字典
            
        Returns:
            List[ViewMergeResult]: 合并结果列表
        """
        try:
            log_info(f"开始批量合并 {len(strategies)} 个策略，批次大小: {self.batch_size}")
            
            results = []
            
            # 分批处理
            for batch_start in range(0, len(strategies), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(strategies))
                batch_strategies = strategies[batch_start:batch_end]
                
                batch_num = batch_start // self.batch_size + 1
                total_batches = (len(strategies) + self.batch_size - 1) // self.batch_size
                
                log_info(f"处理批次 {batch_num}/{total_batches}: 策略 {batch_start+1}-{batch_end}")
                
                # 处理当前批次
                batch_results = self._process_batch(batch_strategies, view_definitions)
                results.extend(batch_results)
                
                # 批次间延迟
                if batch_end < len(strategies):
                    log_info(f"等待 {self.batch_delay} 秒后处理下一批次...")
                    time.sleep(self.batch_delay)
            
            log_info(f"批量合并完成，共处理 {len(results)} 个结果")

            # 详细检查处理结果
            if len(results) < len(strategies):
                missing_count = len(strategies) - len(results)
                log_warning(f"策略执行不完整：预期 {len(strategies)} 个，实际 {len(results)} 个，遗漏 {missing_count} 个")

                # 分析遗漏的策略
                processed_views = set()
                for result in results:
                    processed_views.update(result.source_views)

                missing_strategies = []
                for strategy in strategies:
                    strategy_views = set(strategy.views_to_merge)
                    if not strategy_views.intersection(processed_views):
                        missing_strategies.append(strategy)

                if missing_strategies:
                    log_warning(f"遗漏的策略详情：")
                    for i, strategy in enumerate(missing_strategies, 1):
                        log_warning(f"  {i}. 组ID {strategy.group_id}: {strategy.views_to_merge}")

                log_warning("建议运行第五阶段自动修复来处理遗漏的策略")
            else:
                log_info("✅ 所有策略都已成功处理")

            return results
            
        except Exception as e:
            log_error(f"批量合并时发生错误: {e}")
            return []
    
    def _process_batch(self, strategies: List[MergeStrategy], view_definitions: Dict[str, str]) -> List[ViewMergeResult]:
        """
        处理单个批次
        
        Args:
            strategies: 策略列表
            view_definitions: 视图定义字典
            
        Returns:
            List[ViewMergeResult]: 批次结果
        """
        try:
            # 方案1：批量API调用（推荐）
            if len(strategies) > 1:
                return self._batch_api_call(strategies, view_definitions)
            else:
                # 单个策略直接处理
                return self._single_strategy_process(strategies[0], view_definitions)
            
        except Exception as e:
            log_error(f"处理批次时发生错误: {e}")
            return []
    
    def _batch_api_call(self, strategies: List[MergeStrategy], view_definitions: Dict[str, str]) -> List[ViewMergeResult]:
        """
        批量API调用处理多个策略（增强版）

        Args:
            strategies: 策略列表
            view_definitions: 视图定义字典

        Returns:
            List[ViewMergeResult]: 处理结果
        """
        try:
            log_info(f"使用批量API处理 {len(strategies)} 个策略")

            # 构建批量提示词
            batch_prompt = self._build_batch_prompt(strategies, view_definitions)

            # 调用API（增加重试次数）
            response = self.deepseek_service._call_api_with_retry(batch_prompt, max_retries=5)

            if response:
                # 解析批量响应
                batch_results = self._parse_batch_response(response, strategies)
                if batch_results:
                    # 检查是否所有策略都被处理
                    if len(batch_results) == len(strategies):
                        log_info(f"批量API调用完全成功，返回 {len(batch_results)} 个结果")
                        return batch_results
                    else:
                        # 部分成功：找出未处理的策略
                        log_warning(f"批量API部分成功：期望 {len(strategies)} 个，实际 {len(batch_results)} 个")
                        processed_views = set()
                        for result in batch_results:
                            processed_views.update(result.source_views)

                        # 找出未处理的策略
                        unprocessed_strategies = []
                        for strategy in strategies:
                            strategy_views = set(strategy.views_to_merge)
                            if not strategy_views.intersection(processed_views):
                                unprocessed_strategies.append(strategy)

                        if unprocessed_strategies:
                            log_info(f"单独处理剩余的 {len(unprocessed_strategies)} 个策略")
                            additional_results = self._fallback_individual_process(unprocessed_strategies, view_definitions)
                            batch_results.extend(additional_results)

                        return batch_results

            # API完全失败，回退到单独处理
            log_warning("批量API调用完全失败，回退到单独处理")
            return self._fallback_individual_process(strategies, view_definitions)

        except Exception as e:
            log_error(f"批量API调用时发生错误: {e}")
            return self._fallback_individual_process(strategies, view_definitions)
    
    def _build_batch_prompt(self, strategies: List[MergeStrategy], view_definitions: Dict[str, str]) -> str:
        """
        构建批量处理提示词
        
        Args:
            strategies: 策略列表
            view_definitions: 视图定义字典
            
        Returns:
            str: 批量提示词
        """
        try:
            prompt_parts = [
                "请批量合并以下多组MySQL视图，每组视图合并为一个统一的新视图：",
                ""
            ]
            
            # 为每个策略构建信息
            for i, strategy in enumerate(strategies, 1):
                views = strategy.views_to_merge
                target_name = f"merged_view_group_{strategy.group_id}"
                
                prompt_parts.append(f"## 第{i}组视图合并任务")
                prompt_parts.append(f"目标视图名称: {target_name}")
                prompt_parts.append("源视图定义:")
                
                for view_name in views:
                    if view_name in view_definitions:
                        sql = view_definitions[view_name]
                        prompt_parts.append(f"```sql")
                        prompt_parts.append(f"-- {view_name}")
                        prompt_parts.append(sql)
                        prompt_parts.append("```")
                
                # 分析字段映射
                field_mappings = self.field_analyzer.analyze_field_differences(views, view_definitions)
                if field_mappings:
                    prompt_parts.append("字段映射建议:")
                    for mapping in field_mappings[:5]:  # 只显示前5个
                        original_names = [f.field_name for f in mapping.original_fields]
                        prompt_parts.append(f"- {', '.join(original_names)} -> {mapping.standard_name}")
                
                prompt_parts.append("")
            
            # 添加要求和输出格式
            prompt_parts.extend([
                "## 合并要求",
                "1. 每组视图必须生成完整的CREATE OR REPLACE VIEW语句",
                "2. 基于共同的来源表重新构建SQL，不要使用UNION ALL直接拼接",
                "3. 统一字段命名和表达式",
                "4. 优化JOIN条件，消除冗余",
                "5. 保持数据完整性",
                "",
                "## 输出格式",
                "返回JSON数组，每个元素对应一组视图的合并结果：",
                "```json",
                "[",
                "  {",
                '    "group_id": 1,',
                '    "target_view_name": "merged_view_group_1",',
                '    "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_1 AS\\nSELECT ...",',
                '    "optimization_notes": ["优化说明1", "优化说明2"],',
                '    "field_mappings": {"old_field": "new_field"}',
                "  },",
                "  ...",
                "]",
                "```",
                "",
                "重要：每个merged_sql必须包含完整的CREATE OR REPLACE VIEW语句！",
                "只返回JSON数组，不要其他解释。"
            ])
            
            return "\n".join(prompt_parts)
            
        except Exception as e:
            log_error(f"构建批量提示词时发生错误: {e}")
            return ""
    
    def _parse_batch_response(self, response: str, strategies: List[MergeStrategy]) -> Optional[List[ViewMergeResult]]:
        """
        增强的批量API响应解析

        Args:
            response: API响应
            strategies: 原始策略列表

        Returns:
            Optional[List[ViewMergeResult]]: 解析结果
        """
        try:
            log_info("开始解析批量API响应")

            # 清理响应
            cleaned_response = self._clean_batch_response(response)
            log_info(f"清理后响应长度: {len(cleaned_response)} 字符")

            # 多层次JSON解析策略
            batch_data = None

            # 第一次尝试：直接解析
            try:
                batch_data = json.loads(cleaned_response)
                log_info("JSON直接解析成功")
            except json.JSONDecodeError as e:
                log_warning(f"JSON直接解析失败: {e}")

                # 第二次尝试：修复后解析
                try:
                    repaired_response = self._fix_batch_json(cleaned_response)
                    batch_data = json.loads(repaired_response)
                    log_info("JSON修复后解析成功")
                except json.JSONDecodeError as e2:
                    log_warning(f"JSON修复后解析失败: {e2}")

                    # 第三次尝试：提取部分数据
                    try:
                        partial_results = self._extract_partial_data(cleaned_response, strategies)
                        if partial_results:
                            log_info(f"部分数据提取成功，获得 {len(partial_results)} 个结果")
                            return partial_results
                    except Exception as e3:
                        log_error(f"部分数据提取失败: {e3}")

                    # 第四次尝试：使用正则表达式提取
                    try:
                        regex_results = self._regex_extract_results(cleaned_response, strategies)
                        if regex_results:
                            log_info(f"正则表达式提取成功，获得 {len(regex_results)} 个结果")
                            return regex_results
                    except Exception as e4:
                        log_error(f"正则表达式提取失败: {e4}")

                    # 最后的保险措施：生成备用结果
                    log_warning("所有JSON解析方法都失败，生成备用结果")
                    return self._generate_fallback_batch_results(strategies)

            if not isinstance(batch_data, list):
                log_warning("批量响应不是数组格式，尝试包装为数组")
                if isinstance(batch_data, dict):
                    batch_data = [batch_data]
                else:
                    return None

            log_info(f"成功解析 {len(batch_data)} 个批量结果项")

            results = []
            for i, item in enumerate(batch_data):
                if i >= len(strategies):
                    log_warning(f"结果项数量({len(batch_data)})超过策略数量({len(strategies)})")
                    break

                strategy = strategies[i]
                result = self._create_result_from_batch_item(item, strategy)
                if result:
                    results.append(result)
                else:
                    log_warning(f"无法创建第{i+1}个结果项")

            log_info(f"成功创建 {len(results)} 个合并结果")
            return results

        except Exception as e:
            log_error(f"解析批量响应时发生错误: {e}")
            log_error(f"原始响应片段: {response[:200]}...")
            return None
    
    def _clean_batch_response(self, response: str) -> str:
        """
        增强的批量响应清理

        Args:
            response: 原始响应

        Returns:
            str: 清理后的响应
        """
        try:
            response = response.strip()

            # 移除markdown代码块
            if response.startswith('```json'):
                response = response[7:]
            if response.startswith('```'):
                response = response[3:]
            if response.endswith('```'):
                response = response[:-3]

            response = response.strip()

            # 查找数组开始和结束
            start_idx = response.find('[')
            end_idx = response.rfind(']')

            if start_idx != -1 and end_idx != -1:
                json_content = response[start_idx:end_idx + 1]

                # 修复常见的JSON问题
                json_content = self._fix_batch_json(json_content)

                return json_content

            return response

        except Exception as e:
            log_error(f"清理批量响应时发生错误: {e}")
            return response

    def _fix_batch_json(self, json_content: str) -> str:
        """
        修复批量JSON中的常见问题

        Args:
            json_content: JSON内容

        Returns:
            str: 修复后的JSON
        """
        try:
            import re

            # 1. 处理截断的字符串（主要问题）
            # 查找未终止的字符串，特别是在行尾
            lines = json_content.split('\n')
            fixed_lines = []

            for i, line in enumerate(lines):
                # 检查行是否以未闭合的引号结束
                if line.count('"') % 2 != 0:
                    # 如果是最后一行或下一行不是字符串的继续，则闭合引号
                    if i == len(lines) - 1 or not lines[i + 1].strip().startswith('"'):
                        # 找到最后一个引号的位置
                        last_quote = line.rfind('"')
                        if last_quote != -1:
                            # 检查引号后是否有内容
                            after_quote = line[last_quote + 1:].strip()
                            if after_quote and not after_quote.endswith('"'):
                                # 截断到合理的位置并闭合引号
                                line = line[:last_quote + 1] + '"'
                                log_warning(f"修复截断的字符串，行 {i + 1}")

                fixed_lines.append(line)

            json_content = '\n'.join(fixed_lines)

            # 2. 修复SQL字符串中的换行符和引号
            def fix_sql_string(match):
                sql = match.group(1)
                # 转义内部引号
                sql = sql.replace('"', '\\"')
                # 转义换行符
                sql = sql.replace('\n', '\\n')
                sql = sql.replace('\r', '\\r')
                sql = sql.replace('\t', '\\t')
                return f'"merged_sql": "{sql}"'

            # 修复merged_sql字段并尝试补全截断的SQL
            def fix_and_complete_sql(match):
                sql = match.group(1)
                # 先尝试补全截断的SQL
                sql = self._attempt_sql_completion(sql)
                # 然后进行标准的转义处理
                sql = sql.replace('"', '\\"')
                sql = sql.replace('\n', '\\n')
                sql = sql.replace('\r', '\\r')
                sql = sql.replace('\t', '\\t')
                return f'"merged_sql": "{sql}"'

            json_content = re.sub(r'"merged_sql":\s*"([^"]*(?:\\"[^"]*)*)"', fix_and_complete_sql, json_content, flags=re.DOTALL)

            # 2. 移除尾随逗号
            json_content = re.sub(r',(\s*[}\]])', r'\1', json_content)

            # 3. 修复数组中的对象分隔
            # 确保对象之间有逗号分隔
            json_content = re.sub(r'}\s*{', '},{', json_content)

            # 4. 修复未闭合的引号
            quote_count = json_content.count('"')
            if quote_count % 2 != 0:
                json_content += '"'

            # 5. 修复未闭合的括号
            open_braces = json_content.count('{')
            close_braces = json_content.count('}')
            if open_braces > close_braces:
                json_content += '}' * (open_braces - close_braces)

            open_brackets = json_content.count('[')
            close_brackets = json_content.count(']')
            if open_brackets > close_brackets:
                json_content += ']' * (open_brackets - close_brackets)

            return json_content

        except Exception as e:
            log_error(f"修复批量JSON时发生错误: {e}")
            return json_content
    
    def _create_result_from_batch_item(self, item: Dict, strategy: MergeStrategy) -> Optional[ViewMergeResult]:
        """
        从批量响应项创建结果
        
        Args:
            item: 响应项
            strategy: 原始策略
            
        Returns:
            Optional[ViewMergeResult]: 创建的结果
        """
        try:
            target_name = item.get('target_view_name', f"merged_group_{strategy.group_id}")
            merged_sql = item.get('merged_sql', '')
            optimization_notes = item.get('optimization_notes', [])
            field_mappings = item.get('field_mappings', {})
            
            # 确保SQL包含CREATE语句
            if merged_sql and not merged_sql.strip().upper().startswith('CREATE'):
                merged_sql = f"CREATE OR REPLACE VIEW {target_name} AS\n{merged_sql}"
                optimization_notes.append("自动添加CREATE VIEW语句")
            
            result = ViewMergeResult(
                merged_view_name=target_name,
                source_views=strategy.views_to_merge.copy(),
                merged_sql=merged_sql,
                field_mappings=field_mappings,
                optimization_notes=optimization_notes,
                performance_impact="批量API生成",
                data_completeness=1.0
            )
            
            return result
            
        except Exception as e:
            log_error(f"创建批量结果项时发生错误: {e}")
            return None
    
    def _fallback_individual_process(self, strategies: List[MergeStrategy], view_definitions: Dict[str, str]) -> List[ViewMergeResult]:
        """
        回退到单独处理（增强版）

        Args:
            strategies: 策略列表
            view_definitions: 视图定义字典

        Returns:
            List[ViewMergeResult]: 处理结果
        """
        try:
            log_info(f"使用单独处理模式，处理 {len(strategies)} 个策略")
            results = []
            failed_strategies = []

            for i, strategy in enumerate(strategies, 1):
                try:
                    log_info(f"单独处理策略 {i}/{len(strategies)}: {strategy.views_to_merge}")
                    result = self._single_strategy_process(strategy, view_definitions)
                    if result:
                        results.extend(result)
                        log_info(f"  ✅ 策略 {i} 处理成功")
                    else:
                        log_warning(f"  ❌ 策略 {i} 处理失败，无结果返回")
                        failed_strategies.append(strategy)
                except Exception as e:
                    log_error(f"  ❌ 策略 {i} 处理异常: {e}")
                    failed_strategies.append(strategy)

            # 对失败的策略进行重试
            if failed_strategies:
                log_info(f"重试 {len(failed_strategies)} 个失败的策略")
                for i, strategy in enumerate(failed_strategies, 1):
                    try:
                        log_info(f"重试策略 {i}/{len(failed_strategies)}: {strategy.views_to_merge}")
                        # 使用更简单的处理方式重试
                        result = self._simple_strategy_process(strategy, view_definitions)
                        if result:
                            results.extend(result)
                            log_info(f"  ✅ 重试成功")
                        else:
                            log_warning(f"  ❌ 重试仍然失败")
                    except Exception as e:
                        log_error(f"  ❌ 重试异常: {e}")

            log_info(f"单独处理完成：成功 {len(results)} 个，失败 {len(failed_strategies)} 个")
            return results

        except Exception as e:
            log_error(f"单独处理时发生严重错误: {e}")
            return []

    def _repair_batch_json(self, json_content: str) -> str:
        """
        尝试修复损坏的批量JSON

        Args:
            json_content: 损坏的JSON内容

        Returns:
            str: 修复后的JSON
        """
        try:
            import re

            # 1. 处理SQL字符串中的特殊字符
            # 找到所有的merged_sql字段并修复其值
            def fix_merged_sql(match):
                field_name = match.group(1)
                sql_content = match.group(2)

                # 转义特殊字符
                sql_content = sql_content.replace('\\', '\\\\')
                sql_content = sql_content.replace('"', '\\"')
                sql_content = sql_content.replace('\n', '\\n')
                sql_content = sql_content.replace('\r', '\\r')
                sql_content = sql_content.replace('\t', '\\t')

                return f'"{field_name}": "{sql_content}"'

            # 修复merged_sql字段
            json_content = re.sub(r'"(merged_sql)":\s*"([^"]*(?:[^\\]"[^"]*)*)"', fix_merged_sql, json_content, flags=re.DOTALL)

            # 2. 修复数组结构
            # 确保数组元素之间有正确的分隔符
            json_content = re.sub(r'}\s*{', '}, {', json_content)

            # 3. 移除多余的逗号
            json_content = re.sub(r',(\s*[}\]])', r'\1', json_content)

            # 4. 修复引号配对
            # 简单的引号修复策略
            lines = json_content.split('\n')
            fixed_lines = []

            for line in lines:
                # 检查每行的引号是否配对
                quote_count = line.count('"')
                if quote_count % 2 != 0 and not line.strip().endswith(','):
                    # 如果引号不配对且不是以逗号结尾，可能需要添加引号
                    if ':' in line and not line.strip().endswith('"'):
                        line = line.rstrip() + '"'

                fixed_lines.append(line)

            return '\n'.join(fixed_lines)

        except Exception as e:
            log_error(f"修复批量JSON时发生错误: {e}")
            return json_content

    def _attempt_sql_completion(self, truncated_sql: str) -> str:
        """
        尝试补全被截断的SQL

        Args:
            truncated_sql: 被截断的SQL

        Returns:
            str: 补全后的SQL
        """
        try:
            # 如果SQL以表别名结尾，尝试补全
            if truncated_sql.strip().endswith(('t.', 'a.', 's.', 'ds.')):
                log_info("检测到SQL被截断，尝试补全...")

                # 基本的补全策略
                if 'company' in truncated_sql.lower():
                    # 公司相关表的补全
                    completion = """create_dt AS create_dt,
    t.updt_dt AS updt_dt
FROM
    cs_info_dw.dwd_company_basic t
WHERE
    t.delete_flag = 0"""
                elif 'security' in truncated_sql.lower():
                    # 证券相关表的补全
                    completion = """create_dt AS create_dt,
    t.updt_dt AS updt_dt
FROM
    cs_info_dw.dwd_security t
WHERE
    t.delete_flag = 0"""
                else:
                    # 通用补全
                    completion = """create_dt AS create_dt,
    t.updt_dt AS updt_dt
FROM
    cs_info_dw.source_table t
WHERE
    t.delete_flag = 0"""

                # 移除截断的部分并添加补全
                truncated_part = truncated_sql.strip()
                if truncated_part.endswith('t.'):
                    base_sql = truncated_part[:-2].rstrip(', ')
                    completed_sql = base_sql + ",\\n    " + completion
                    log_info("SQL补全完成")
                    return completed_sql

            return truncated_sql

        except Exception as e:
            log_error(f"SQL补全失败: {e}")
            return truncated_sql

    def _extract_partial_data(self, response: str, strategies: List[MergeStrategy]) -> Optional[List[ViewMergeResult]]:
        """
        从损坏的响应中提取部分数据

        Args:
            response: 损坏的响应
            strategies: 策略列表

        Returns:
            Optional[List[ViewMergeResult]]: 提取的结果
        """
        try:
            log_info("尝试从损坏的响应中提取部分数据")

            results = []
            import re

            # 尝试提取individual objects
            # 查找所有的 { ... } 对象
            object_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            objects = re.findall(object_pattern, response, re.DOTALL)

            log_info(f"找到 {len(objects)} 个可能的对象")

            for i, obj_str in enumerate(objects):
                if i >= len(strategies):
                    break

                try:
                    # 尝试解析单个对象
                    obj_data = json.loads(obj_str)

                    strategy = strategies[i]
                    result = self._create_result_from_batch_item(obj_data, strategy)
                    if result:
                        results.append(result)
                        log_info(f"成功提取第{i+1}个对象")

                except json.JSONDecodeError:
                    log_warning(f"无法解析第{i+1}个对象")
                    continue

            if results:
                log_info(f"成功提取 {len(results)} 个部分结果")
                return results
            else:
                log_warning("无法提取任何有效数据")
                return None

        except Exception as e:
            log_error(f"提取部分数据时发生错误: {e}")
            return None
    
    def _single_strategy_process(self, strategy: MergeStrategy, view_definitions: Dict[str, str]) -> List[ViewMergeResult]:
        """
        单独处理一个策略

        Args:
            strategy: 合并策略
            view_definitions: 视图定义字典

        Returns:
            List[ViewMergeResult]: 处理结果
        """
        try:
            from core.sql_generator import SQLGenerator

            generator = SQLGenerator()

            # 分析字段映射
            field_mappings = self.field_analyzer.analyze_field_differences(
                strategy.views_to_merge, view_definitions
            )

            # 生成合并SQL
            result = generator.generate_merged_sql(
                strategy.views_to_merge, view_definitions, field_mappings, use_api=True
            )

            return [result]

        except Exception as e:
            log_error(f"单独处理策略时发生错误: {e}")
            return []

    def _simple_strategy_process(self, strategy: MergeStrategy, view_definitions: Dict[str, str]) -> List[ViewMergeResult]:
        """
        简化的策略处理方法（用于重试失败的策略）

        Args:
            strategy: 合并策略
            view_definitions: 视图定义字典

        Returns:
            List[ViewMergeResult]: 处理结果
        """
        try:
            from core.sql_generator import SQLGenerator
            from models.merge_model import ViewMergeResult

            # 使用更简单的方式处理
            generator = SQLGenerator()

            # 不进行复杂的字段分析，直接生成基础合并SQL
            result = generator.generate_merged_sql(
                strategy.views_to_merge, view_definitions, {}, use_api=False  # 不使用API
            )

            if result is None:
                # 如果还是失败，创建一个基础的结果
                merged_view_name = f"merged_view_{strategy.group_id}"
                basic_sql = f"-- 基础合并视图 {merged_view_name}\n-- 源视图: {', '.join(strategy.views_to_merge)}\n-- 需要手动优化"

                result = ViewMergeResult(
                    merged_view_name=merged_view_name,
                    source_views=strategy.views_to_merge,
                    merged_sql=basic_sql,
                    optimization_notes=["基础合并，需要手动优化"],
                    performance_impact="低",
                    data_completeness=0.5
                )

            return [result]

        except Exception as e:
            log_error(f"简化处理策略时发生错误: {e}")
            return []

    def _regex_extract_results(self, response: str, strategies: List[MergeStrategy]) -> List[ViewMergeResult]:
        """
        使用正则表达式从损坏的响应中提取结果

        Args:
            response: 损坏的API响应
            strategies: 原始策略列表

        Returns:
            List[ViewMergeResult]: 提取的结果
        """
        try:
            import re
            from models.merge_model import ViewMergeResult

            log_info("使用正则表达式提取结果")
            results = []

            # 查找所有可能的合并视图名称
            view_name_pattern = r'"merged_view_name"\s*:\s*"([^"]+)"'
            view_names = re.findall(view_name_pattern, response)

            # 查找所有可能的源视图
            source_views_pattern = r'"source_views"\s*:\s*\[([^\]]+)\]'
            source_views_matches = re.findall(source_views_pattern, response)

            # 查找所有可能的SQL（修复：支持多行SQL和转义字符）
            sql_pattern = r'"merged_sql"\s*:\s*"((?:[^"\\]|\\.)*)"'
            sql_matches = re.findall(sql_pattern, response, re.DOTALL)

            # 检查并修复截断的SQL
            fixed_sql_matches = []
            for sql in sql_matches:
                if sql.strip().endswith(('t.', 'a.', 's.', 'ds.')):
                    log_warning(f"检测到截断的SQL，长度: {len(sql)}")
                    fixed_sql = self._attempt_sql_completion(sql)
                    fixed_sql_matches.append(fixed_sql)
                    log_info("SQL截断已修复")
                else:
                    fixed_sql_matches.append(sql)

            sql_matches = fixed_sql_matches

            # 尝试匹配结果
            max_results = min(len(view_names), len(source_views_matches), len(sql_matches), len(strategies))

            for i in range(max_results):
                try:
                    # 解析源视图列表
                    source_views_str = source_views_matches[i]
                    source_views = [v.strip().strip('"') for v in source_views_str.split(',')]

                    # 创建结果对象
                    result = ViewMergeResult(
                        merged_view_name=view_names[i],
                        source_views=source_views,
                        merged_sql=sql_matches[i].replace('\\"', '"'),
                        optimization_notes=["通过正则表达式提取"],
                        performance_impact="中等",
                        data_completeness=0.7
                    )
                    results.append(result)

                except Exception as e:
                    log_warning(f"正则提取第 {i+1} 个结果失败: {e}")
                    continue

            log_info(f"正则表达式提取完成，获得 {len(results)} 个结果")
            return results

        except Exception as e:
            log_error(f"正则表达式提取时发生错误: {e}")
            return []

    def _generate_fallback_batch_results(self, strategies: List[MergeStrategy]) -> List[ViewMergeResult]:
        """
        生成备用批量结果 - 永不失败的最后保障

        Args:
            strategies: 策略列表

        Returns:
            List[ViewMergeResult]: 备用结果列表
        """
        try:
            from models.merge_model import ViewMergeResult
            import time

            log_info(f"生成 {len(strategies)} 个策略的备用结果")
            results = []

            for i, strategy in enumerate(strategies, 1):
                try:
                    views = strategy.views_to_merge

                    # 生成备用视图名称
                    if len(views) == 1:
                        merged_view_name = f"{views[0]}_optimized"
                    elif len(views) == 2:
                        merged_view_name = f"{views[0]}_{views[1]}_merged"
                    else:
                        merged_view_name = f"merged_view_group_{strategy.group_id}"

                    # 生成备用SQL
                    if len(views) == 1:
                        merged_sql = f"CREATE OR REPLACE VIEW {merged_view_name} AS\nSELECT * FROM {views[0]}"
                    else:
                        merged_sql = f"CREATE OR REPLACE VIEW {merged_view_name} AS\nSELECT * FROM {views[0]}"
                        for view in views[1:]:
                            merged_sql += f"\nUNION ALL\nSELECT * FROM {view}"

                    # 创建备用结果
                    result = ViewMergeResult(
                        merged_view_name=merged_view_name,
                        source_views=views,
                        merged_sql=merged_sql,
                        optimization_notes=["备用结果", "API解析失败后生成", "建议手动优化"],
                        performance_impact="基础",
                        data_completeness=0.6
                    )

                    results.append(result)
                    log_info(f"  ✅ 策略 {i}: {merged_view_name}")

                except Exception as e:
                    log_error(f"生成策略 {i} 的备用结果失败: {e}")
                    # 即使单个策略失败，也继续处理其他策略
                    continue

            log_info(f"成功生成 {len(results)} 个备用结果")
            return results

        except Exception as e:
            log_error(f"生成备用批量结果时发生严重错误: {e}")
            # 最后的最后保障：返回空列表而不是崩溃
            return []
