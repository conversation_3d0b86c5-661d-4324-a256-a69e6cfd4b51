{"extraction_time": "2025-08-04T12:56:31.222202", "total_views": 4, "view_ddls": {"bond_basicinfo_dw": "CREATE OR REPLACE VIEW bond_basicinfo_dw AS\nselect `bond_basicinfo`.`security_global_id` AS `security_global_id`,`bond_basicinfo`.`secinner_id` AS `secinner_id`,`bond_basicinfo`.`security_cscs_code` AS `security_cscs_code`,`bond_basicinfo`.`company_global_id` AS `company_global_id`,`bond_basicinfo`.`company_id` AS `company_id`,`bond_basicinfo`.`security_cd` AS `security_cd`,`bond_basicinfo`.`security_nm` AS `security_nm`,`bond_basicinfo`.`security_snm` AS `security_snm`,`bond_basicinfo`.`spell` AS `spell`,`bond_basicinfo`.`security_type_id` AS `security_type_id`,`bond_basicinfo`.`issue_year` AS `issue_year`,`bond_basicinfo`.`issue_num` AS `issue_num`,`bond_basicinfo`.`currency` AS `currency`,`bond_basicinfo`.`trade_market_id` AS `trade_market_id`,`bond_basicinfo`.`notice_dt` AS `notice_dt`,`bond_basicinfo`.`src_create_dt` AS `src_create_dt`,`bond_basicinfo`.`public_dt` AS `public_dt`,`bond_basicinfo`.`public_announce_dt` AS `public_announce_dt`,`bond_basicinfo`.`issue_dt` AS `issue_dt`,`bond_basicinfo`.`frst_value_dt` AS `frst_value_dt`,`bond_basicinfo`.`last_value_dt` AS `last_value_dt`,`bond_basicinfo`.`puttable_dt` AS `puttable_dt`,`bond_basicinfo`.`mrty_dt` AS `mrty_dt`,`bond_basicinfo`.`payment_dt` AS `payment_dt`,`bond_basicinfo`.`redem_dt` AS `redem_dt`,`bond_basicinfo`.`delist_dt` AS `delist_dt`,`bond_basicinfo`.`pay_day` AS `pay_day`,`bond_basicinfo`.`bond_type1_id` AS `bond_type1_id`,`bond_basicinfo`.`bond_type2_id` AS `bond_type2_id`,`bond_basicinfo`.`bond_form_id` AS `bond_form_id`,`bond_basicinfo`.`other_nature` AS `other_nature`,`bond_basicinfo`.`credit_rating` AS `credit_rating`,`bond_basicinfo`.`issue_vol` AS `issue_vol`,`bond_basicinfo`.`listvolsz` AS `listvolsz`,`bond_basicinfo`.`listvolsh` AS `listvolsh`,`bond_basicinfo`.`bond_period` AS `bond_period`,`bond_basicinfo`.`par_value` AS `par_value`,`bond_basicinfo`.`issue_price` AS `issue_price`,`bond_basicinfo`.`coupon_type_cd` AS `coupon_type_cd`,`bond_basicinfo`.`pay_type_cd` AS `pay_type_cd`,`bond_basicinfo`.`pay_desc` AS `pay_desc`,`bond_basicinfo`.`coupon_rule_cd` AS `coupon_rule_cd`,`bond_basicinfo`.`payment_type_cd` AS `payment_type_cd`,`bond_basicinfo`.`coupon_rate` AS `coupon_rate`,`bond_basicinfo`.`floor_rate` AS `floor_rate`,`bond_basicinfo`.`bnchmk_spread` AS `bnchmk_spread`,`bond_basicinfo`.`bnchmk_id` AS `bnchmk_id`,`bond_basicinfo`.`rate_desc` AS `rate_desc`,`bond_basicinfo`.`pay_peryear` AS `pay_peryear`,`bond_basicinfo`.`add_rate` AS `add_rate`,`bond_basicinfo`.`puttable_price` AS `puttable_price`,`bond_basicinfo`.`expect_rate` AS `expect_rate`,`bond_basicinfo`.`issue_type_cd` AS `issue_type_cd`,`bond_basicinfo`.`refe_rate` AS `refe_rate`,`bond_basicinfo`.`add_issue_num` AS `add_issue_num`,`bond_basicinfo`.`is_cross` AS `is_cross`,`bond_basicinfo`.`is_floor_rate` AS `is_floor_rate`,`bond_basicinfo`.`is_adjust_type` AS `is_adjust_type`,`bond_basicinfo`.`is_redem` AS `is_redem`,`bond_basicinfo`.`is_plit_debt` AS `is_plit_debt`,`bond_basicinfo`.`is_puttable` AS `is_puttable`,`bond_basicinfo`.`is_change` AS `is_change`,`bond_basicinfo`.`fwd_rate` AS `fwd_rate`,`bond_basicinfo`.`redem_price` AS `redem_price`,`bond_basicinfo`.`swaps_cd` AS `swaps_cd`,`bond_basicinfo`.`tax_rate` AS `tax_rate`,`bond_basicinfo`.`coupon_style_cd` AS `coupon_style_cd`,`bond_basicinfo`.`Option_Termdes` AS `option_termdes`,`bond_basicinfo`.`Base_Asset` AS `base_asset`,`bond_basicinfo`.`coupon_method_cd` AS `coupon_method_cd`,`bond_basicinfo`.`bond_option` AS `bond_option`,`bond_basicinfo`.`credit_typein_id` AS `credit_typein_id`,`bond_basicinfo`.`credit_typeout_id` AS `credit_typeout_id`,`bond_basicinfo`.`remark` AS `remark`,`bond_basicinfo`.`src_portfolio_cd` AS `src_portfolio_cd`,`bond_basicinfo`.`src_isdel` AS `src_isdel`,`bond_basicinfo`.`SRC_CD` AS `src_cd`,`bond_basicinfo`.`IS_DEL` AS `is_del`,`bond_basicinfo`.`CREATE_DT` AS `create_dt`,`bond_basicinfo`.`UPDT_DT` AS `updt_dt`,`bond_basicinfo`.`is_abs` AS `is_abs`,`bond_basicinfo`.`is_sub` AS `is_sub` from (select `a`.`security_global_id` AS `security_global_id`,`a`.`security_global_id` AS `secinner_id`,`a`.`security_cscs_code` AS `security_cscs_code`,`a`.`company_global_id` AS `company_global_id`,`a`.`company_global_id` AS `company_id`,`a`.`security_code` AS `security_cd`,`a`.`security_name` AS `security_nm`,`a`.`security_short_name` AS `security_snm`,`a`.`spell` AS `spell`,ifnull(`mp`.`dictionary_relation_code`,`a`.`security_type_id`) AS `security_type_id`,`a`.`issue_year` AS `issue_year`,`a`.`issue_num` AS `issue_num`,ifnull(`c2`.`dictionary_relation_code`,`a`.`currency`) AS `currency`,ifnull(`c3`.`dictionary_relation_code`,`a`.`trade_market_id`) AS `trade_market_id`,`a`.`notice_date` AS `notice_dt`,`a`.`create_date` AS `src_create_dt`,`a`.`public_date` AS `public_dt`,`a`.`public_announce_date` AS `public_announce_dt`,`a`.`issue_date` AS `issue_dt`,ifnull(`a`.`first_value_date`,`a`.`issue_date`) AS `frst_value_dt`,`a`.`last_value_date` AS `last_value_dt`,`a`.`put_table_date` AS `puttable_dt`,`a`.`maturity_date` AS `mrty_dt`,`a`.`payment_date` AS `payment_dt`,`a`.`redeem_date` AS `redem_dt`,`a`.`delist_date` AS `delist_dt`,`a`.`pay_day` AS `pay_day`,`mp`.`dictionary_relation_code` AS `bond_type1_id`,`mp`.`dictionary_relation_code` AS `bond_type2_id`,`a`.`bond_form_id` AS `bond_form_id`,`a`.`other_nature` AS `other_nature`,`a`.`credit_rating` AS `credit_rating`,`a`.`issue_vol` AS `issue_vol`,`a`.`list_vol_sz` AS `listvolsz`,`a`.`list_vol_sh` AS `listvolsh`,`a`.`bond_period` AS `bond_period`,`a`.`par_value` AS `par_value`,`a`.`issue_price` AS `issue_price`,`a`.`coupon_type_code` AS `coupon_type_cd`,`a`.`pay_type_code` AS `pay_type_cd`,`a`.`pay_desc` AS `pay_desc`,`a`.`coupon_rule_code` AS `coupon_rule_cd`,`a`.`payment_type_code` AS `payment_type_cd`,`a`.`coupon_rate` AS `coupon_rate`,`a`.`floor_rate` AS `floor_rate`,`a`.`bnchmk_spread` AS `bnchmk_spread`,`a`.`bnchmk_id` AS `bnchmk_id`,`a`.`rate_desc` AS `rate_desc`,`a`.`pay_per_year` AS `pay_peryear`,`a`.`add_rate` AS `add_rate`,`a`.`put_table_price` AS `puttable_price`,`a`.`expect_rate` AS `expect_rate`,`a`.`issue_type_code` AS `issue_type_cd`,`a`.`reference_rate` AS `refe_rate`,`a`.`add_issue_num` AS `add_issue_num`,`a`.`is_cross` AS `is_cross`,`a`.`is_floor_rate` AS `is_floor_rate`,`a`.`is_adjust_type` AS `is_adjust_type`,`a`.`is_redeem` AS `is_redem`,`a`.`is_plit_debt` AS `is_plit_debt`,`a`.`is_put_table` AS `is_puttable`,`a`.`is_change` AS `is_change`,`a`.`fwd_rate` AS `fwd_rate`,`a`.`redeem_price` AS `redem_price`,`a`.`swaps_code` AS `swaps_cd`,`a`.`tax_rate` AS `tax_rate`,`a`.`coupon_style_code` AS `coupon_style_cd`,`a`.`option_termdes` AS `Option_Termdes`,`a`.`base_asset` AS `Base_Asset`,`a`.`coupon_method_code` AS `coupon_method_cd`,`a`.`bond_option` AS `bond_option`,`a`.`credit_type_in_id` AS `credit_typein_id`,`a`.`credit_type_out_id` AS `credit_typeout_id`,`a`.`remark` AS `remark`,`a`.`src_portfolio_cd` AS `src_portfolio_cd`,`a`.`delete_flag` AS `src_isdel`,ifnull(`a`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`a`.`delete_flag` AS `IS_DEL`,`a`.`create_dt` AS `CREATE_DT`,`a`.`updt_dt` AS `UPDT_DT`,(case when (ifnull(`mp`.`mapping_relation_code`,`mq`.`mapping_relation_code`) like '060007%') then 1 else 0 end) AS `is_abs`,`a`.`is_sub` AS `is_sub` from ((((((`cs_info_dw`.`dwd_bond_basic_info` `a` left join (select `n`.`dictionary_relation_code` AS `dictionary_relation_code`,`n`.`mapping_relation_code` AS `mapping_relation_code`,`n`.`source_code` AS `source_code`,`n`.`delete_flag` AS `delete_flag`,`n`.`dictionary_relation` AS `dictionary_relation` from (select `p`.`dictionary_relation_code` AS `dictionary_relation_code`,`p`.`mapping_relation_code` AS `mapping_relation_code`,`p`.`source_code` AS `source_code`,`p`.`delete_flag` AS `delete_flag`,`p`.`dictionary_relation` AS `dictionary_relation`,row_number() OVER (PARTITION BY `p`.`dictionary_relation_code` ORDER BY `p`.`dictionary_relation_id` )  AS `RN` from `cs_info_dw`.`dim_dictionary_relation` `p` where ((`p`.`dictionary_relation` = 'SECURITY_TYPE') and (`p`.`source_code` = 'CSCS'))) `n` where (`n`.`RN` = 1)) `mq` on(((`mq`.`dictionary_relation` = 'SECURITY_TYPE') and (`a`.`security_type_id` = `mq`.`dictionary_relation_code`) and (`mq`.`source_code` = 'CSCS') and (`mq`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `dlcc` on(((`a`.`security_type_id` = cast(`dlcc`.`constant_id` as char charset utf8mb4)) and (`dlcc`.`constant_type` = 201) and (`dlcc`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `mp` on(((`mp`.`dictionary_relation` = 'SECURITY_TYPE') and (`dlcc`.`constant_code` = `mp`.`mapping_relation_code`) and (`mp`.`source_code` = 'CSCS') and (`mp`.`delete_flag` = 0) and (((`a`.`issue_type_code` = 1) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010201)) or ((`a`.`issue_type_code` = 2) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010202)) or ((`dlcc`.`constant_code` <> '060005007') and (1 = 1)))))) left join `cs_info_dw`.`dim_dictionary_relation` `c2` on(((`a`.`currency` = `c2`.`mapping_relation_code`) and (`c2`.`dictionary_relation` = 'COMPY_BASICINFO.CURRENCY') and (`c2`.`source_code` = 'CSCS.MDS') and (`c2`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `c` on(((`c`.`constant_id` = `a`.`trade_market_id`) and (`c`.`constant_type` = 206) and (`c`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `c3` on(((`c`.`constant_code` = `c3`.`mapping_relation_code`) and (`c3`.`dictionary_relation` = 'MARKET_TYPE') and (`c3`.`source_code` = 'CSCS') and (`c3`.`delete_flag` = 0)))) where (`a`.`delete_flag` = 0)) `bond_basicinfo`", "bond_pledge_cm": "CREATE OR REPLACE VIEW bond_pledge_cm AS\nselect `bond_pledge`.`bond_pledge_sid` AS `bond_pledge_sid`,`bond_pledge`.`security_global_id` AS `security_global_id`,`bond_pledge`.`secinner_id` AS `secinner_id`,`bond_pledge`.`notice_dt` AS `notice_dt`,`bond_pledge`.`pledge_nm` AS `pledge_nm`,`bond_pledge`.`pledge_type_id` AS `pledge_type_id`,`bond_pledge`.`pledge_desc` AS `pledge_desc`,`bond_pledge`.`pledge_owner_code` AS `pledge_owner_code`,`bond_pledge`.`pledge_owner` AS `pledge_owner`,`bond_pledge`.`pledge_value` AS `pledge_value`,`bond_pledge`.`priority_value` AS `priority_value`,`bond_pledge`.`pledge_depend_id` AS `pledge_depend_id`,`bond_pledge`.`pledge_control_id` AS `pledge_control_id`,`bond_pledge`.`region` AS `region`,`bond_pledge`.`mitigation_value` AS `mitigation_value`,`bond_pledge`.`src_isdel` AS `src_isdel`,`bond_pledge`.`SRC_CD` AS `src_cd`,`bond_pledge`.`IS_DEL` AS `is_del`,`bond_pledge`.`CREATE_DT` AS `create_dt`,`bond_pledge`.`UPDT_DT` AS `updt_dt` from (select `t`.`pk_id` AS `bond_pledge_sid`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `secinner_id`,`t`.`notice_date` AS `notice_dt`,`t`.`pledge_name` AS `pledge_nm`,`t`.`pledge_type_id` AS `pledge_type_id`,`t`.`pledge_desc` AS `pledge_desc`,`t`.`pledge_owner_code` AS `pledge_owner_code`,`t`.`pledge_owner` AS `pledge_owner`,`t`.`pledge_value` AS `pledge_value`,`t`.`priority_value` AS `priority_value`,`t`.`pledge_depend_id` AS `pledge_depend_id`,`t`.`pledge_control_id` AS `pledge_control_id`,`t`.`region` AS `region`,`t`.`mitigation_value` AS `mitigation_value`,`t`.`delete_flag` AS `src_isdel`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_pledge` `t` where (`t`.`SOURCE_CODE` = 'MANUAL')) `bond_pledge`", "bond_pledge": "CREATE OR REPLACE VIEW bond_pledge AS\nselect `s`.`BOND_PLEDGE_SID` AS `BOND_PLEDGE_SID`,`s`.`security_global_id` AS `security_global_id`,`s`.`SECINNER_ID` AS `SECINNER_ID`,`s`.`NOTICE_DT` AS `NOTICE_DT`,`s`.`PLEDGE_NM` AS `PLEDGE_NM`,`s`.`PLEDGE_TYPE_ID` AS `PLEDGE_TYPE_ID`,`s`.`PLEDGE_DESC` AS `PLEDGE_DESC`,`s`.`PLEDGE_OWNER_CODE` AS `PLEDGE_OWNER_CODE`,`s`.`PLEDGE_OWNER` AS `PLEDGE_OWNER`,`s`.`PLEDGE_VALUE` AS `PLEDGE_VALUE`,`s`.`PRIORITY_VALUE` AS `PRIORITY_VALUE`,`s`.`PLED<PERSON>_DEPEND_ID` AS `PLEDGE_DEPEND_ID`,`s`.`PLEDGE_CONTROL_ID` AS `PLEDGE_CONTROL_ID`,`s`.`REGION` AS `REGION`,`s`.`MITIGATION_VALUE` AS `MITIGATION_VALUE`,`s`.`SRC_ISDEL` AS `SRC_ISDEL`,`s`.`SRC_CD` AS `SRC_CD`,`s`.`IS_DEL` AS `IS_DEL`,`s`.`CREATE_DT` AS `CREATE_DT`,`s`.`UPDT_DT` AS `UPDT_DT` from (select `d`.`BOND_PLEDGE_SID` AS `BOND_PLEDGE_SID`,`d`.`security_global_id` AS `security_global_id`,`d`.`SECINNER_ID` AS `SECINNER_ID`,`d`.`NOTICE_DT` AS `NOTICE_DT`,`d`.`PLEDGE_NM` AS `PLEDGE_NM`,`d`.`PLEDGE_TYPE_ID` AS `PLEDGE_TYPE_ID`,`d`.`PLEDGE_DESC` AS `PLEDGE_DESC`,`d`.`PLEDGE_OWNER_CODE` AS `PLEDGE_OWNER_CODE`,`d`.`PLEDGE_OWNER` AS `PLEDGE_OWNER`,`d`.`PLEDGE_VALUE` AS `PLEDGE_VALUE`,`d`.`PRIORITY_VALUE` AS `PRIORITY_VALUE`,`d`.`PLEDGE_DEPEND_ID` AS `PLEDGE_DEPEND_ID`,`d`.`PLEDGE_CONTROL_ID` AS `PLEDGE_CONTROL_ID`,`d`.`REGION` AS `REGION`,`d`.`MITIGATION_VALUE` AS `MITIGATION_VALUE`,`d`.`SRC_ISDEL` AS `SRC_ISDEL`,`d`.`SRC_CD` AS `SRC_CD`,`d`.`IS_DEL` AS `IS_DEL`,`d`.`CREATE_DT` AS `CREATE_DT`,`d`.`UPDT_DT` AS `UPDT_DT`,row_number() OVER (PARTITION BY `d`.`security_global_id`,`d`.`PLEDGE_NM` ORDER BY `d`.`UPDT_DT` desc,`d`.`CREATE_DT` desc )  AS `RN` from (select `t`.`pk_id` AS `BOND_PLEDGE_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`pledge_name` AS `PLEDGE_NM`,`t`.`pledge_type_id` AS `PLEDGE_TYPE_ID`,`t`.`pledge_desc` AS `PLEDGE_DESC`,`t`.`pledge_owner_code` AS `PLEDGE_OWNER_CODE`,`t`.`pledge_owner` AS `PLEDGE_OWNER`,`t`.`pledge_value` AS `PLEDGE_VALUE`,`t`.`priority_value` AS `PRIORITY_VALUE`,`t`.`pledge_depend_id` AS `PLEDGE_DEPEND_ID`,`t`.`pledge_control_id` AS `PLEDGE_CONTROL_ID`,`t`.`region` AS `REGION`,`t`.`mitigation_value` AS `MITIGATION_VALUE`,`t`.`delete_flag` AS `SRC_ISDEL`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_pledge` `t`) `d`) `s` where (`s`.`RN` = 1)", "bond_basicinfo": "CREATE OR REPLACE VIEW bond_basicinfo AS\nselect `bond_basicinfo`.`security_global_id` AS `security_global_id`,`bond_basicinfo`.`secinner_id` AS `secinner_id`,`bond_basicinfo`.`security_cscs_code` AS `security_cscs_code`,`bond_basicinfo`.`company_global_id` AS `company_global_id`,`bond_basicinfo`.`company_id` AS `company_id`,`bond_basicinfo`.`security_cd` AS `security_cd`,`bond_basicinfo`.`security_nm` AS `security_nm`,`bond_basicinfo`.`security_snm` AS `security_snm`,`bond_basicinfo`.`spell` AS `spell`,`bond_basicinfo`.`security_type_id` AS `security_type_id`,`bond_basicinfo`.`issue_year` AS `issue_year`,`bond_basicinfo`.`issue_num` AS `issue_num`,`bond_basicinfo`.`currency` AS `currency`,`bond_basicinfo`.`trade_market_id` AS `trade_market_id`,`bond_basicinfo`.`notice_dt` AS `notice_dt`,`bond_basicinfo`.`src_create_dt` AS `src_create_dt`,`bond_basicinfo`.`public_dt` AS `public_dt`,`bond_basicinfo`.`public_announce_dt` AS `public_announce_dt`,`bond_basicinfo`.`issue_dt` AS `issue_dt`,`bond_basicinfo`.`frst_value_dt` AS `frst_value_dt`,`bond_basicinfo`.`last_value_dt` AS `last_value_dt`,`bond_basicinfo`.`puttable_dt` AS `puttable_dt`,`bond_basicinfo`.`mrty_dt` AS `mrty_dt`,`bond_basicinfo`.`payment_dt` AS `payment_dt`,`bond_basicinfo`.`redem_dt` AS `redem_dt`,`bond_basicinfo`.`delist_dt` AS `delist_dt`,`bond_basicinfo`.`pay_day` AS `pay_day`,`bond_basicinfo`.`bond_type1_id` AS `bond_type1_id`,`bond_basicinfo`.`bond_type2_id` AS `bond_type2_id`,`bond_basicinfo`.`bond_form_id` AS `bond_form_id`,`bond_basicinfo`.`other_nature` AS `other_nature`,`bond_basicinfo`.`credit_rating` AS `credit_rating`,`bond_basicinfo`.`issue_vol` AS `issue_vol`,`bond_basicinfo`.`listvolsz` AS `listvolsz`,`bond_basicinfo`.`listvolsh` AS `listvolsh`,`bond_basicinfo`.`bond_period` AS `bond_period`,`bond_basicinfo`.`par_value` AS `par_value`,`bond_basicinfo`.`issue_price` AS `issue_price`,`bond_basicinfo`.`coupon_type_cd` AS `coupon_type_cd`,`bond_basicinfo`.`pay_type_cd` AS `pay_type_cd`,`bond_basicinfo`.`pay_desc` AS `pay_desc`,`bond_basicinfo`.`coupon_rule_cd` AS `coupon_rule_cd`,`bond_basicinfo`.`payment_type_cd` AS `payment_type_cd`,`bond_basicinfo`.`coupon_rate` AS `coupon_rate`,`bond_basicinfo`.`floor_rate` AS `floor_rate`,`bond_basicinfo`.`bnchmk_spread` AS `bnchmk_spread`,`bond_basicinfo`.`bnchmk_id` AS `bnchmk_id`,`bond_basicinfo`.`rate_desc` AS `rate_desc`,`bond_basicinfo`.`pay_peryear` AS `pay_peryear`,`bond_basicinfo`.`add_rate` AS `add_rate`,`bond_basicinfo`.`puttable_price` AS `puttable_price`,`bond_basicinfo`.`expect_rate` AS `expect_rate`,`bond_basicinfo`.`issue_type_cd` AS `issue_type_cd`,`bond_basicinfo`.`refe_rate` AS `refe_rate`,`bond_basicinfo`.`add_issue_num` AS `add_issue_num`,`bond_basicinfo`.`is_cross` AS `is_cross`,`bond_basicinfo`.`is_floor_rate` AS `is_floor_rate`,`bond_basicinfo`.`is_adjust_type` AS `is_adjust_type`,`bond_basicinfo`.`is_redem` AS `is_redem`,`bond_basicinfo`.`is_plit_debt` AS `is_plit_debt`,`bond_basicinfo`.`is_puttable` AS `is_puttable`,`bond_basicinfo`.`is_change` AS `is_change`,`bond_basicinfo`.`fwd_rate` AS `fwd_rate`,`bond_basicinfo`.`redem_price` AS `redem_price`,`bond_basicinfo`.`swaps_cd` AS `swaps_cd`,`bond_basicinfo`.`tax_rate` AS `tax_rate`,`bond_basicinfo`.`coupon_style_cd` AS `coupon_style_cd`,`bond_basicinfo`.`Option_Termdes` AS `option_termdes`,`bond_basicinfo`.`Base_Asset` AS `base_asset`,`bond_basicinfo`.`coupon_method_cd` AS `coupon_method_cd`,`bond_basicinfo`.`bond_option` AS `bond_option`,`bond_basicinfo`.`credit_typein_id` AS `credit_typein_id`,`bond_basicinfo`.`credit_typeout_id` AS `credit_typeout_id`,`bond_basicinfo`.`remark` AS `remark`,`bond_basicinfo`.`src_portfolio_cd` AS `src_portfolio_cd`,`bond_basicinfo`.`src_isdel` AS `src_isdel`,`bond_basicinfo`.`SRC_CD` AS `src_cd`,`bond_basicinfo`.`IS_DEL` AS `is_del`,`bond_basicinfo`.`CREATE_DT` AS `create_dt`,`bond_basicinfo`.`UPDT_DT` AS `updt_dt`,`bond_basicinfo`.`is_abs` AS `is_abs`,`bond_basicinfo`.`is_sub` AS `is_sub`,`bond_basicinfo`.`is_continuous` AS `is_continuous` from (select `a`.`security_global_id` AS `security_global_id`,`a`.`security_global_id` AS `secinner_id`,`a`.`security_cscs_code` AS `security_cscs_code`,`a`.`company_global_id` AS `company_global_id`,`a`.`company_global_id` AS `company_id`,`a`.`security_code` AS `security_cd`,`a`.`security_name` AS `security_nm`,`a`.`security_short_name` AS `security_snm`,`a`.`spell` AS `spell`,ifnull(`mp`.`dictionary_relation_code`,`a`.`security_type_id`) AS `security_type_id`,`a`.`issue_year` AS `issue_year`,`a`.`issue_num` AS `issue_num`,ifnull(`c2`.`dictionary_relation_code`,`a`.`currency`) AS `currency`,ifnull(`c3`.`dictionary_relation_code`,`a`.`trade_market_id`) AS `trade_market_id`,`a`.`notice_date` AS `notice_dt`,`a`.`create_date` AS `src_create_dt`,`a`.`public_date` AS `public_dt`,`a`.`public_announce_date` AS `public_announce_dt`,`a`.`issue_date` AS `issue_dt`,ifnull(`a`.`first_value_date`,`a`.`issue_date`) AS `frst_value_dt`,`a`.`last_value_date` AS `last_value_dt`,`a`.`put_table_date` AS `puttable_dt`,`a`.`maturity_date` AS `mrty_dt`,`a`.`payment_date` AS `payment_dt`,`a`.`redeem_date` AS `redem_dt`,`a`.`delist_date` AS `delist_dt`,`a`.`pay_day` AS `pay_day`,`mp`.`dictionary_relation_code` AS `bond_type1_id`,`mp`.`dictionary_relation_code` AS `bond_type2_id`,`a`.`bond_form_id` AS `bond_form_id`,`a`.`other_nature` AS `other_nature`,`a`.`credit_rating` AS `credit_rating`,`a`.`issue_vol` AS `issue_vol`,`a`.`list_vol_sz` AS `listvolsz`,`a`.`list_vol_sh` AS `listvolsh`,`a`.`bond_period` AS `bond_period`,`a`.`par_value` AS `par_value`,`a`.`issue_price` AS `issue_price`,`a`.`coupon_type_code` AS `coupon_type_cd`,`a`.`pay_type_code` AS `pay_type_cd`,`a`.`pay_desc` AS `pay_desc`,`a`.`coupon_rule_code` AS `coupon_rule_cd`,`a`.`payment_type_code` AS `payment_type_cd`,`a`.`coupon_rate` AS `coupon_rate`,`a`.`floor_rate` AS `floor_rate`,`a`.`bnchmk_spread` AS `bnchmk_spread`,`a`.`bnchmk_id` AS `bnchmk_id`,`a`.`rate_desc` AS `rate_desc`,`a`.`pay_per_year` AS `pay_peryear`,`a`.`add_rate` AS `add_rate`,`a`.`put_table_price` AS `puttable_price`,`a`.`expect_rate` AS `expect_rate`,`a`.`issue_type_code` AS `issue_type_cd`,`a`.`reference_rate` AS `refe_rate`,`a`.`add_issue_num` AS `add_issue_num`,`a`.`is_cross` AS `is_cross`,`a`.`is_floor_rate` AS `is_floor_rate`,`a`.`is_adjust_type` AS `is_adjust_type`,`a`.`is_redeem` AS `is_redem`,`a`.`is_plit_debt` AS `is_plit_debt`,`a`.`is_put_table` AS `is_puttable`,`a`.`is_change` AS `is_change`,`a`.`fwd_rate` AS `fwd_rate`,`a`.`redeem_price` AS `redem_price`,`a`.`swaps_code` AS `swaps_cd`,`a`.`tax_rate` AS `tax_rate`,`a`.`coupon_style_code` AS `coupon_style_cd`,`a`.`option_termdes` AS `Option_Termdes`,`a`.`base_asset` AS `Base_Asset`,`a`.`coupon_method_code` AS `coupon_method_cd`,`a`.`bond_option` AS `bond_option`,`a`.`credit_type_in_id` AS `credit_typein_id`,`a`.`credit_type_out_id` AS `credit_typeout_id`,`a`.`remark` AS `remark`,`a`.`src_portfolio_cd` AS `src_portfolio_cd`,`a`.`delete_flag` AS `src_isdel`,ifnull(`a`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`a`.`delete_flag` AS `IS_DEL`,`a`.`create_dt` AS `CREATE_DT`,`a`.`updt_dt` AS `UPDT_DT`,(case when (ifnull(`mp`.`mapping_relation_code`,`mq`.`mapping_relation_code`) like '060007%') then 1 else 0 end) AS `is_abs`,`a`.`is_sub` AS `is_sub`,(case when (`a`.`option_termdes` like '%+N') then 1 else 0 end) AS `is_continuous` from ((((((`cs_info_dw`.`dwd_bond_basic_info` `a` left join (select `n`.`dictionary_relation_code` AS `dictionary_relation_code`,`n`.`mapping_relation_code` AS `mapping_relation_code`,`n`.`source_code` AS `source_code`,`n`.`delete_flag` AS `delete_flag`,`n`.`dictionary_relation` AS `dictionary_relation` from (select `p`.`dictionary_relation_code` AS `dictionary_relation_code`,`p`.`mapping_relation_code` AS `mapping_relation_code`,`p`.`source_code` AS `source_code`,`p`.`delete_flag` AS `delete_flag`,`p`.`dictionary_relation` AS `dictionary_relation`,row_number() OVER (PARTITION BY `p`.`dictionary_relation_code` ORDER BY `p`.`dictionary_relation_id` )  AS `RN` from `cs_info_dw`.`dim_dictionary_relation` `p` where ((`p`.`dictionary_relation` = 'SECURITY_TYPE') and (`p`.`source_code` = 'CSCS'))) `n` where (`n`.`RN` = 1)) `mq` on(((`mq`.`dictionary_relation` = 'SECURITY_TYPE') and (`a`.`security_type_id` = `mq`.`dictionary_relation_code`) and (`mq`.`source_code` = 'CSCS') and (`mq`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `dlcc` on(((`a`.`security_type_id` = cast(`dlcc`.`constant_id` as char charset utf8mb4)) and (`dlcc`.`constant_type` = 201) and (`dlcc`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `mp` on(((`mp`.`dictionary_relation` = 'SECURITY_TYPE') and (`dlcc`.`constant_code` = `mp`.`mapping_relation_code`) and (`mp`.`source_code` = 'CSCS') and (`mp`.`delete_flag` = 0) and (((`a`.`issue_type_code` = 1) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010201)) or ((`a`.`issue_type_code` = 2) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010202)) or ((`dlcc`.`constant_code` <> '060005007') and (1 = 1)))))) left join `cs_info_dw`.`dim_dictionary_relation` `c2` on(((`a`.`currency` = `c2`.`mapping_relation_code`) and (`c2`.`dictionary_relation` = 'COMPY_BASICINFO.CURRENCY') and (`c2`.`source_code` = 'CSCS.MDS') and (`c2`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `c` on(((`c`.`constant_id` = `a`.`trade_market_id`) and (`c`.`constant_type` = 206) and (`c`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `c3` on(((`c`.`constant_code` = `c3`.`mapping_relation_code`) and (`c3`.`dictionary_relation` = 'MARKET_TYPE') and (`c3`.`source_code` = 'CSCS') and (`c3`.`delete_flag` = 0)))) where (`a`.`delete_flag` = 0)) `bond_basicinfo`"}}