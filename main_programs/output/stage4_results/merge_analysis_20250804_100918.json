{"merge_strategies": [{"merge_id": 1, "merged_view_name": "bond_pledge_bond_pledge_cm_merged", "source_views": ["bond_pledge", "bond_pledge_cm"], "source_view_count": 2, "field_mapping_count": 39, "optimization_count": 7, "data_completeness": 1.0, "has_sql": true}], "field_standardization": {"total_field_mappings": 39, "unique_standard_fields": 20, "mapping_efficiency": 0.5128205128205128}, "sql_optimizations": {"total_optimizations": 7, "successful_sql_generation": 1, "failed_sql_generation": 0}, "processing_summary": {"total_merge_tasks": 1, "total_field_mappings": 39, "total_optimizations": 7, "average_data_completeness": 1.0, "reduction_rate": 0.5, "successful_merges": 1, "failed_merges": 0, "processing_time": "2025-08-04T10:08:32.377443", "input_files": {"classification_result": "output/stage3_results\\classification_result_20250804_100824.json"}}}