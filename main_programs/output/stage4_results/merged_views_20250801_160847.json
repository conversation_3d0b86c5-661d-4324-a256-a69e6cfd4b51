{"merged_views": [{"merged_view_name": "merged_view_group_2", "source_views": ["bond_pledge", "bond_pledge_cm"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_2 AS\nSELECT \n    `t`.`pk_id` AS `bond_pledge_sid`,\n    `t`.`security_global_id` AS `security_global_id`,\n    `t`.`security_global_id` AS `secinner_id`,\n    `t`.`notice_date` AS `notice_dt`,\n    `t`.`pledge_name` AS `pledge_nm`,\n    `t`.`pledge_type_id` AS `pledge_type_id`,\n    `t`.`pledge_desc` AS `pledge_desc`,\n    `t`.`pledge_owner_code` AS `pledge_owner_code`,\n    `t`.`pledge_owner` AS `pledge_owner`,\n    `t`.`pledge_value` AS `pledge_value`,\n    `t`.`priority_value` AS `priority_value`,\n    `t`.`pledge_depend_id` AS `pledge_depend_id`,\n    `t`.`pledge_control_id` AS `pledge_control_id`,\n    `t`.`region` AS `region`,\n    `t`.`mitigation_value` AS `mitigation_value`,\n    `t`.`delete_flag` AS `src_isdel`,\n    IFNULL(`t`.`SOURCE_CODE`,'CSCS') AS `src_cd`,\n    `t`.`delete_flag` AS `is_del`,\n    `t`.`create_dt` AS `create_dt`,\n    `t`.`updt_dt` AS `updt_dt`\nFROM `cs_info_dw`.`dwd_bond_pledge` `t`\nWHERE `t`.`SOURCE_CODE` = 'MANUAL' OR ROW_NUMBER() OVER (PARTITION BY `t`.`security_global_id`,`t`.`pledge_name` ORDER BY `t`.`updt_dt` DESC, `t`.`create_dt` DESC) = 1", "field_mappings": {"BOND_PLEDGE_SID": "bond_pledge_sid", "bond_pledge_sid": "bond_pledge_sid", "security_global_id": "security_global_id", "SECINNER_ID": "secinner_id", "secinner_id": "secinner_id", "NOTICE_DT": "notice_dt", "notice_dt": "notice_dt", "PLEDGE_NM": "pledge_nm", "pledge_nm": "pledge_nm"}, "optimization_notes": ["合并了bond_pledge和bond_pledge_cm视图，统一了字段命名", "使用ROW_NUMBER()窗口函数替代子查询，提高性能", "保留了所有数据源，通过条件过滤确保数据完整性"], "created_time": "2025-08-01T16:06:08.152367", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_3", "source_views": ["cfg_dict_mapping_rela", "dict_abs_mapping_rela"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_3 AS\nSELECT \n    `t`.`dictionary_relation_id` AS `dict_mapping_rela_id`,\n    `t`.`dictionary_relation` AS `dict_rela_id`,\n    `t`.`dictionary_relation_name` AS `dict_rela_name`,\n    `t`.`dictionary_relation_code` AS `sec_r_code`,\n    `t`.`mapping_relation_code` AS `mapping_r_code`,\n    `t`.`order_num` AS `order_num`,\n    `t`.`dictionary_relation_rule` AS `dict_rela_rule`,\n    `t`.`remark` AS `remark`,\n    `t`.`source_code` AS `src_cd`,\n    `t`.`create_time` AS `updt_dt`,\n    `t`.`delete_flag` AS `is_del`,\n    `t`.`create_time` AS `create_dt`,\n    0 AS `status`,\n    1100 AS `create_by`,\n    1100 AS `updt_by`,\n    1 AS `version`\nFROM `cs_info_dw`.`dim_dictionary_relation` `t`\nWHERE `t`.`dictionary_relation` IS NOT NULL", "field_mappings": {"DICT_MAPPING_RELA_ID": "dict_mapping_rela_id", "DICT_RELA_ID": "dict_rela_id", "DICT_RELA_NAME": "dict_rela_name", "SEC_R_CODE": "sec_r_code", "MAPPING_R_CODE": "mapping_r_code"}, "optimization_notes": ["合并了cfg_dict_mapping_rela和dict_abs_mapping_rela视图", "统一了字段命名和表达式", "保留了所有字段，包括两个视图特有的字段", "简化了查询结构，直接从源表获取数据"], "created_time": "2025-08-01T16:06:08.152367", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_4", "source_views": ["company_basicinfo", "compy_basicinfo"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_4 AS\nSELECT \n    `t`.`company_global_id` AS `compy_basicinfo_sid`,\n    `t`.`company_global_id` AS `company_global_id`,\n    `t`.`company_global_id` AS `company_id`,\n    COALESCE(`t`.`company_code`, LPAD(`t`.`company_global_id`, 15, 0)) AS `company_cd`,\n    `t`.`company_name_cn` AS `company_nm`,\n    `t`.`company_short_name` AS `company_snm`,\n    `t`.`cleans_company_name` AS `clens_company_nm`,\n    `t`.`company_name_en` AS `fen_nm`,\n    `t`.`leg_represent` AS `leg_represent`,\n    `t`.`chairman` AS `chairman`,\n    `t`.`general_manager` AS `gmanager`,\n    `t`.`board_secretary` AS `bsecretary`,\n    `t`.`organization_form` AS `org_form_id`,\n    `t`.`found_date` AS `found_dt`,\n    `t`.`register_capital_currency` AS `currency`,\n    `t`.`register_capital` AS `reg_capital`,\n    `t`.`register_country` AS `country`,\n    `t`.`register_region` AS `region`,\n    `t`.`register_city` AS `city`,\n    `t`.`register_address` AS `reg_addr`,\n    `t`.`company_address` AS `office_addr`,\n    `t`.`company_zip_code` AS `office_post_cd`,\n    `t`.`company_phone` AS `company_ph`,\n    `t`.`company_fax` AS `company_fax`,\n    `t`.`company_email` AS `company_em`,\n    `t`.`company_web` AS `company_web`,\n    `t`.`business_scope` AS `busin_scope`,\n    `t`.`main_business` AS `main_busin`,\n    `t`.`employ_number` AS `employ_num`,\n    `t`.`business_license_number` AS `blnumb`,\n    `t`.`national_tax_number` AS `ntrnum`,\n    `t`.`local_tax_number` AS `ltrnum`,\n    `t`.`organization_code` AS `orgnum`,\n    `t`.`register_date` AS `reg_dt`,\n    `t`.`info_url` AS `info_url`,\n    `t`.`info_news` AS `info_news`,\n    `t`.`accounting_firm` AS `accounting_firm`,\n    `t`.`legal_advisor` AS `legal_advisor`,\n    `t`.`company_state` AS `company_st`,\n    `t`.`company_profile` AS `company_profile`,\n    NULL AS `src_company_cd`,\n    IFNULL(`t`.`SOURCE_CODE`,'CSCS') AS `src_cd`,\n    `t`.`delete_flag` AS `is_del`,\n    `t`.`create_dt` AS `create_dt`,\n    `t`.`updt_dt` AS `updt_dt`,\n    `t`.`is_core` AS `is_core`,\n    `t`.`actual_capital` AS `actual_capital`,\n    `t`.`business_term_start` AS `start_dt`,\n    `t`.`business_term_end` AS `end_dt`,\n    IFNULL(`a`.`dictionary_name`,`t`.`company_state`) AS `orig_company_st`,\n    IFNULL(`b`.`dictionary_name`,`t`.`organization_form`) AS `orig_org_form`,\n    `t`.`register_government` AS `reg_gov`,\n    `t`.`revoke_date` AS `revoke_dt`,\n    NULL AS `src_updt_dt`,\n    `t`.`company_code` AS `cscs_code`\nFROM `cs_info_dw`.`dwd_company_basic` `t`\nLEFT JOIN `cs_info_dw`.`dim_dictionary_info` `a` ON `a`.`dictionary_type` = 'COMPY_BASICINFO.COMPANY_ST' AND `t`.`company_state` = `a`.`dictionary_code` AND `a`.`delete_flag` = 0\nLEFT JOIN `cs_info_dw`.`dim_dictionary_info` `b` ON `b`.`dictionary_type` = 'COMPY_ALL_INFO.ORG_FORM_ID' AND `t`.`organization_form` = `b`.`dictionary_code` AND `b`.`delete_flag` = 0\nWHERE `t`.`delete_flag` = 0", "field_mappings": {"COMPY_BASICINFO_SID": "compy_basicinfo_sid", "company_global_id": "company_global_id", "COMPANY_GLOBAL_ID": "company_global_id", "COMPANY_ID": "company_id", "COMPANY_CD": "company_cd", "COMPANY_NM": "company_nm"}, "optimization_notes": ["合并了company_basicinfo和compy_basicinfo视图", "使用COALESCE函数处理company_cd字段的不同来源", "保留了所有字段，包括两个视图特有的字段", "优化了JOIN条件，确保数据完整性"], "created_time": "2025-08-01T16:06:08.152367", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_5", "source_views": ["company_core", "compy_core"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_5 AS\nSELECT \n  t.company_global_id AS compy_core_sid,\n  t.company_global_id AS company_global_id,\n  a.company_code AS cscs_code,\n  t.company_global_id AS company_id,\n  a.company_name_cn AS company_nm,\n  t.is_list AS IS_LIST,\n  t.list_type AS LIST_TYPE,\n  t.is_new_otc AS IS_NEW_OTC,\n  t.is_bond AS IS_BOND,\n  t.is_finance AS IS_FINANCE,\n  t.finance_type AS FIN_TYPE,\n  t.finance_type_subsidiary AS FIN_TYPE_SUPPV,\n  t.is_it_finance AS IS_ITFIN,\n  t.delete_flag AS SRC_ISDEL,\n  'CSCS' AS SRC_CD,\n  t.delete_flag AS IS_DEL,\n  t.create_dt AS CREATE_DT,\n  t.updt_dt AS UPDT_DT\nFROM \n  cs_info_dw.dwd_company_core t\n  JOIN cs_info_dw.dwd_company_basic a ON t.company_global_id = a.company_global_id AND a.delete_flag = 0", "field_mappings": {"COMPY_CORE_SID": "compy_core_sid", "company_global_id": "company_global_id", "cscs_code": "cscs_code", "COMPANY_ID": "company_id", "COMPANY_NM": "company_nm"}, "optimization_notes": ["统一了字段命名，使用小写下划线格式", "合并了两个视图的查询逻辑，保留了所有字段", "优化了JOIN条件，去除了冗余条件"], "created_time": "2025-08-01T16:07:23.793228", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_6", "source_views": ["company_shareholders_opt", "compy_shareholders_optimized"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_6 AS\nSELECT \n  t.pk_id AS company_shareholders_opt_id,\n  t.company_global_id AS company_global_id,\n  t.company_global_id AS company_id,\n  t.shareholder_name AS shareholder_name,\n  t.shareholder_id AS shareholder_global_id,\n  t.shareholder_id AS shareholder_id,\n  t.person_global_id AS PERSON_ID,\n  t.shareholder_type AS SHAREHOLDER_TYPE,\n  str_to_date(t.report_date,'%Y%m%d') AS RPT_DT,\n  t.shareholder_ratio AS SHAREHOLDER_RATIO,\n  t.subscribed_capital_amount AS SUBSCRIBED_CAPITAL_AMT,\n  str_to_date(t.subscribed_date,'%Y%m%d') AS SUBSCRIBED_DT,\n  t.subscribed_type AS SUBSCRIBED_TYPE,\n  t.paid_capital_amount AS PAID_CAPITAL_AMT,\n  str_to_date(t.paid_date,'%Y%m%d') AS PAID_DT,\n  t.paid_type AS PAID_TYPE,\n  t.shareholder_cate AS SHAREHOLDER_CATE,\n  'CSCS' AS SRC_CD,\n  t.delete_flag AS IS_DEL,\n  t.create_dt AS CREATE_DT,\n  t.updt_dt AS UPDT_DT\nFROM \n  cs_info_dw.dwd_company_shareholders_opt t", "field_mappings": {"COMPANY_SHAREHOLDERS_OPT_ID": "company_shareholders_opt_id", "company_global_id": "company_global_id", "company_id": "company_id", "SHAREHOLDER_NAME": "shareholder_name", "shareholder_global_id": "shareholder_global_id"}, "optimization_notes": ["统一了日期字段的格式处理", "合并了两个视图的字段，选择更完整的字段集合", "标准化了字段命名"], "created_time": "2025-08-01T16:07:23.793228", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_7", "source_views": ["dim_security", "dwd_security"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_7 AS\nSELECT \n  ds.security_global_id AS security_global_id,\n  ds.security_cscs_code AS security_cscs_code,\n  ds.security_code AS security_code,\n  ds.security_name AS security_name,\n  ds.security_short_name AS security_short_name,\n  ds.spell AS spell,\n  ds.security_type_id AS security_type_id,\n  ds.security_type AS security_type,\n  ds.trade_market_id AS trade_market_id,\n  ds.market_type AS market_type,\n  ds.company_code AS company_code,\n  ds.company_global_id AS company_global_id,\n  ds.list_status AS list_status,\n  ds.use_status AS use_status,\n  ds.currency AS currency,\n  ds.list_date AS list_date,\n  ds.end_date AS end_date,\n  COALESCE(ds.SOURCE_ID, ds.security_global_id) AS SOURCE_ID,\n  COALESCE(ds.SOURCE_CODE, 'CSCS') AS SOURCE_CODE,\n  ds.delete_flag AS delete_flag,\n  1100 AS CREATE_BY,\n  ds.create_dt AS CREATE_TIME,\n  1100 AS UPDATE_BY,\n  ds.updt_dt AS UPDATE_TIME,\n  0 AS VERSION\nFROM \n  cs_info_dw.dwd_security ds", "field_mappings": {"SECURITY_GLOBAL_ID": "security_global_id", "SECURITY_CSCS_CODE": "security_cscs_code", "SECURITY_CODE": "security_code", "SECURITY_NAME": "security_name", "SECURITY_SHORT_NAME": "security_short_name"}, "optimization_notes": ["合并了两个视图的字段，选择更完整的字段集合", "统一了SOURCE_ID和SOURCE_CODE的处理逻辑", "标准化了字段命名"], "created_time": "2025-08-01T16:07:23.793228", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_3views", "source_views": ["dim_company_basic", "dim_company_basic_detail", "dwd_company_basic"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_3views AS\nSELECT \n    t.company_global_id AS company_global_id,\n    t.company_code AS company_code,\n    t.company_name_cn AS company_name_cn,\n    t.company_short_name AS company_short_name,\n    t.company_name_en AS company_name_en,\n    t.leg_represent AS leg_represent,\n    t.chairman AS chairman,\n    t.general_manager AS general_manager,\n    t.board_secretary AS board_secretary,\n    t.organization_form AS organization_form,\n    t.organization_code AS organization_code,\n    t.register_capital AS register_capital,\n    t.register_capital_currency AS register_capital_currency,\n    t.register_government AS register_government,\n    t.register_address AS register_address,\n    t.register_country AS register_country,\n    t.register_region AS register_region,\n    t.register_city AS register_city,\n    t.business_license_number AS business_license_number,\n    t.national_tax_number AS national_tax_number,\n    t.local_tax_number AS local_tax_number,\n    COALESCE(t.source_id, NULL) AS source_id,\n    COALESCE(t.source_code, 'CSCS') AS source_code,\n    t.delete_flag AS delete_flag,\n    COALESCE(t.create_by, 1100) AS create_by,\n    COALESCE(t.create_time, t.create_dt) AS create_time,\n    COALESCE(t.update_by, 1100) AS update_by,\n    COALESCE(t.update_time, t.updt_dt) AS update_time,\n    t.version AS version,\n    t.is_core AS is_core,\n    t.company_state AS company_status,\n    t.register_area AS register_area,\n    t.company_name_spell AS company_name_spell,\n    t.person_global_id AS person_global_id,\n    t.found_date AS found_date,\n    t.register_date AS register_date,\n    t.actual_capital AS actual_capital,\n    t.company_address AS company_address,\n    t.company_zip_code AS company_zip_code,\n    t.company_phone AS company_phone,\n    t.company_fax AS company_fax,\n    t.company_email AS company_email,\n    t.company_web AS company_web,\n    t.business_scope AS business_scope,\n    t.main_business AS main_business,\n    t.employ_number AS employ_number,\n    t.info_url AS info_url,\n    t.info_news AS info_news,\n    t.accounting_firm AS accounting_firm,\n    t.legal_advisor AS legal_advisor,\n    t.company_state AS company_state,\n    t.company_profile AS company_profile,\n    t.business_term_start AS business_term_start,\n    t.business_term_end AS business_term_end,\n    t.revoke_date AS revoke_date,\n    t.cleans_company_name AS cleans_company_name,\n    t.origin_organization_form AS origin_organization_form,\n    t.status_flag AS status_flag,\n    t.managing_partner_global_id AS managing_partner_global_id,\n    t.company_global_id AS company_basic_id\nFROM \n    cs_info_dw.dwd_company_basic t", "field_mappings": {"company_global_id": "company_global_id", "company_code": "company_code", "company_name_cn": "company_name_cn", "company_short_name": "company_short_name", "company_name_en": "company_name_en", "leg_represent": "leg_represent", "chairman": "chairman", "general_manager": "general_manager", "board_secretary": "board_secretary", "organization_form": "organization_form", "organization_code": "organization_code", "register_capital": "register_capital", "register_capital_currency": "register_capital_currency", "register_government": "register_government", "register_address": "register_address", "register_country": "register_country", "register_region": "register_region", "register_city": "register_city", "business_license_number": "business_license_number", "national_tax_number": "national_tax_number", "local_tax_number": "local_tax_number", "source_id": "source_id", "source_code": "source_code", "delete_flag": "delete_flag", "create_by": "create_by", "create_time": "create_time", "update_by": "update_by", "update_time": "update_time", "version": "version", "is_core": "is_core", "company_status": "company_status", "register_area": "register_area", "company_name_spell": "company_name_spell", "person_global_id": "person_global_id", "found_date": "found_date", "register_date": "register_date", "actual_capital": "actual_capital", "company_address": "company_address", "company_zip_code": "company_zip_code", "company_phone": "company_phone", "company_fax": "company_fax", "company_email": "company_email", "company_web": "company_web", "business_scope": "business_scope", "main_business": "main_business", "employ_number": "employ_number", "info_url": "info_url", "info_news": "info_news", "accounting_firm": "accounting_firm", "legal_advisor": "legal_advisor", "company_state": "company_state", "company_profile": "company_profile", "business_term_start": "business_term_start", "business_term_end": "business_term_end", "revoke_date": "revoke_date", "create_dt": "create_dt", "updt_dt": "updt_dt", "cleans_company_name": "cleans_company_name", "origin_organization_form": "origin_organization_form", "status_flag": "status_flag", "managing_partner_global_id": "managing_partner_global_id", "COMPANY_BASIC_ID": "company_basic_id"}, "optimization_notes": ["统一了所有字段的命名和表达式", "使用COALESCE函数处理NULL值和默认值", "直接从源表查询，避免视图嵌套", "合并了所有三个视图的字段", "使用API生成"], "created_time": "2025-08-01T16:07:24.803381", "performance_impact": "性能应优于原始视图，因为直接从源表查询且避免了视图嵌套", "data_completeness": 1.0}], "summary": {"total_original_views": 15, "total_merged_views": 7, "reduction_rate": 0.0, "processing_time": "2025-08-01T16:08:47.031614", "total_merge_tasks": 7, "total_field_mappings": 97, "total_optimizations": 25, "average_data_completeness": 1.0, "successful_merges": 7, "failed_merges": 0}}