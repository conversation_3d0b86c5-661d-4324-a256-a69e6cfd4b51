{"merged_views": [{"merged_view_name": "merged_view_group_2", "source_views": ["bond_pledge", "bond_pledge_cm"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_2 AS\nSELECT \n    `t`.`pk_id` AS `_B_O_N_D_P_L_E_D_G_E_S_I_D`,\n    `t`.`security_global_id` AS `_S_E_C_U_R_I_T_Y_G_L_O_B_A_L_I_D`,\n    `t`.`security_global_id` AS `_S_E_C_I_N_N_E_R_I_D`,\n    `t`.`notice_date` AS `_N_O_T_I_C_E_D_T`,\n    `t`.`pledge_name` AS `_P_L_E_D_G_E_N_M`,\n    `t`.`pledge_type_id` AS `PLEDGE_TYPE_ID`,\n    `t`.`pledge_desc` AS `PLEDGE_DESC`,\n    `t`.`pledge_owner_code` AS `PLEDGE_OWNER_CODE`,\n    `t`.`pledge_owner` AS `PLEDGE_OWNER`,\n    `t`.`pledge_value` AS `PLEDGE_VALUE`,\n    `t`.`priority_value` AS `PRIORITY_VALUE`,\n    `t`.`pledge_depend_id` AS `PLEDGE_DEPEND_ID`,\n    `t`.`pledge_control_id` AS `PLEDGE_CONTROL_ID`,\n    `t`.`region` AS `REGION`,\n    `t`.`mitigation_value` AS `MITIGATION_VALUE`,\n    `t`.`delete_flag` AS `SRC_ISDEL`,\n    IFNULL(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,\n    `t`.`delete_flag` AS `IS_DEL`,\n    `t`.`create_dt` AS `CREATE_DT`,\n    `t`.`updt_dt` AS `UPDT_DT`,\n    CASE WHEN `t`.`SOURCE_CODE` = 'MANUAL' THEN 1 ELSE 0 END AS `IS_MANUAL`\nFROM `cs_info_dw`.`dwd_bond_pledge` `t`", "field_mappings": {"BOND_PLEDGE_SID": "_B_O_N_D_P_L_E_D_G_E_S_I_D", "bond_pledge_sid": "_B_O_N_D_P_L_E_D_G_E_S_I_D", "security_global_id": "_S_E_C_U_R_I_T_Y_G_L_O_B_A_L_I_D", "SECINNER_ID": "_S_E_C_I_N_N_E_R_I_D", "secinner_id": "_S_E_C_I_N_N_E_R_I_D", "NOTICE_DT": "_N_O_T_I_C_E_D_T", "notice_dt": "_N_O_T_I_C_E_D_T", "PLEDGE_NM": "_P_L_E_D_G_E_N_M", "pledge_nm": "_P_L_E_D_G_E_N_M"}, "optimization_notes": ["合并了bond_pledge和bond_pledge_cm视图，统一了字段命名", "添加了IS_MANUAL字段来区分手动录入的数据", "移除了子查询和ROW_NUMBER()窗口函数，直接从源表查询"], "created_time": "2025-08-01T17:04:37.713376", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_3", "source_views": ["cfg_dict_mapping_rela", "dict_abs_mapping_rela"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_3 AS\nSELECT \n    `t`.`dictionary_relation_id` AS `_D_I_C_T_M_A_P_P_I_N_G_R_E_L_A_I_D`,\n    `t`.`dictionary_relation` AS `_D_I_C_T_R_E_L_A_I_D`,\n    `t`.`dictionary_relation_name` AS `_D_I_C_T_R_E_L_A_N_A_M_E`,\n    `t`.`dictionary_relation_code` AS `_S_E_C_R_C_O_D_E`,\n    `t`.`mapping_relation_code` AS `_M_A_P_P_I_N_G_R_C_O_D_E`,\n    `t`.`order_num` AS `ORDER_NUM`,\n    `t`.`dictionary_relation_rule` AS `DICT_RELA_RULE`,\n    `t`.`remark` AS `REMARK`,\n    `t`.`source_code` AS `SRC_CD`,\n    `t`.`create_time` AS `CREATE_DT`,\n    `t`.`update_time` AS `UPDT_DT`,\n    `t`.`delete_flag` AS `IS_DEL`,\n    0 AS `STATUS`,\n    1100 AS `create_by`,\n    1100 AS `UPDT_BY`,\n    1 AS `VERSION`\nFROM `cs_info_dw`.`dim_dictionary_relation` `t`\nWHERE `t`.`dictionary_relation` IS NOT NULL", "field_mappings": {"DICT_MAPPING_RELA_ID": "_D_I_C_T_M_A_P_P_I_N_G_R_E_L_A_I_D", "DICT_RELA_ID": "_D_I_C_T_R_E_L_A_I_D", "DICT_RELA_NAME": "_D_I_C_T_R_E_L_A_N_A_M_E", "SEC_R_CODE": "_S_E_C_R_C_O_D_E", "MAPPING_R_CODE": "_M_A_P_P_I_N_G_R_C_O_D_E"}, "optimization_notes": ["合并了cfg_dict_mapping_rela和dict_abs_mapping_rela视图", "统一了字段命名和表达式", "保留了所有视图的字段，包括STATUS、create_by等可选字段"], "created_time": "2025-08-01T17:04:37.714390", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_4", "source_views": ["company_basicinfo", "compy_basicinfo"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_4 AS\nSELECT \n    `t`.`company_global_id` AS `_C_O_M_P_Y_B_A_S_I_C_I_N_F_O_S_I_D`,\n    `t`.`company_global_id` AS `_C_O_M_P_A_N_Y_G_L_O_B_A_L_I_D`,\n    `t`.`company_global_id` AS `_C_O_M_P_A_N_Y_I_D`,\n    COALESCE(`t`.`company_code`, LPAD(`t`.`company_global_id`, 15, 0)) AS `_C_O_M_P_A_N_Y_C_D`,\n    `t`.`company_name_cn` AS `_C_O_M_P_A_N_Y_N_M`,\n    `t`.`company_short_name` AS `COMPANY_SNM`,\n    `t`.`cleans_company_name` AS `CLENS_COMPANY_NM`,\n    `t`.`company_name_en` AS `FEN_NM`,\n    `t`.`leg_represent` AS `LEG_REPRESENT`,\n    `t`.`chairman` AS `CHAIRMAN`,\n    `t`.`general_manager` AS `GMANAGER`,\n    `t`.`board_secretary` AS `BSECRETARY`,\n    `t`.`organization_form` AS `ORG_FORM_ID`,\n    `t`.`found_date` AS `FOUND_DT`,\n    `t`.`register_capital_currency` AS `CURRENCY`,\n    `t`.`register_capital` AS `REG_CAPITAL`,\n    `t`.`register_country` AS `COUNTRY`,\n    `t`.`register_region` AS `REGION`,\n    `t`.`register_city` AS `CITY`,\n    `t`.`register_address` AS `REG_ADDR`,\n    `t`.`company_address` AS `OFFICE_ADDR`,\n    `t`.`company_zip_code` AS `OFFICE_POST_CD`,\n    `t`.`company_phone` AS `COMPANY_PH`,\n    `t`.`company_fax` AS `COMPANY_FAX`,\n    `t`.`company_email` AS `COMPANY_EM`,\n    `t`.`company_web` AS `COMPANY_WEB`,\n    `t`.`business_scope` AS `BUSIN_SCOPE`,\n    `t`.`main_business` AS `MAIN_BUSIN`,\n    `t`.`employ_number` AS `EMPLOY_NUM`,\n    `t`.`business_license_number` AS `BLNUMB`,\n    `t`.`national_tax_number` AS `NTRNUM`,\n    `t`.`local_tax_number` AS `LTRNUM`,\n    `t`.`organization_code` AS `ORGNUM`,\n    `t`.`register_date` AS `REG_DT`,\n    `t`.`info_url` AS `INFO_URL`,\n    `t`.`info_news` AS `INFO_NEWS`,\n    `t`.`accounting_firm` AS `ACCOUNTING_FIRM`,\n    `t`.`legal_advisor` AS `LEGAL_ADVISOR`,\n    `t`.`company_state` AS `COMPANY_ST`,\n    `t`.`company_profile` AS `COMPANY_PROFILE`,\n    NULL AS `SRC_COMPANY_CD`,\n    IFNULL(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,\n    `t`.`delete_flag` AS `IS_DEL`,\n    `t`.`create_dt` AS `CREATE_DT`,\n    `t`.`updt_dt` AS `UPDT_DT`,\n    `t`.`is_core` AS `IS_CORE`,\n    `t`.`actual_capital` AS `ACTUAL_CAPITAL`,\n    `t`.`business_term_start` AS `START_DT`,\n    `t`.`business_term_end` AS `END_DT`,\n    IFNULL(`a`.`dictionary_name`,`t`.`company_state`) AS `ORIG_COMPANY_ST`,\n    IFNULL(`b`.`dictionary_name`,`t`.`organization_form`) AS `ORIG_ORG_FORM`,\n    `t`.`register_government` AS `REG_GOV`,\n    `t`.`revoke_date` AS `REVOKE_DT`,\n    NULL AS `SRC_UPDT_DT`,\n    `t`.`company_code` AS `CSCS_CODE`\nFROM `cs_info_dw`.`dwd_company_basic` `t`\nLEFT JOIN `cs_info_dw`.`dim_dictionary_info` `a` \n    ON `a`.`dictionary_type` = 'COMPY_BASICINFO.COMPANY_ST' \n    AND `t`.`company_state` = `a`.`dictionary_code` \n    AND `a`.`delete_flag` = 0\nLEFT JOIN `cs_info_dw`.`dim_dictionary_info` `b` \n    ON `b`.`dictionary_type` = 'COMPY_ALL_INFO.ORG_FORM_ID' \n    AND `t`.`organization_form` = `b`.`dictionary_code` \n    AND `b`.`delete_flag` = 0\nWHERE `t`.`delete_flag` = 0", "field_mappings": {"COMPY_BASICINFO_SID": "_C_O_M_P_Y_B_A_S_I_C_I_N_F_O_S_I_D", "company_global_id": "_C_O_M_P_A_N_Y_G_L_O_B_A_L_I_D", "COMPANY_GLOBAL_ID": "_C_O_M_P_A_N_Y_G_L_O_B_A_L_I_D", "COMPANY_ID": "_C_O_M_P_A_N_Y_I_D", "COMPANY_CD": "_C_O_M_P_A_N_Y_C_D", "COMPANY_NM": "_C_O_M_P_A_N_Y_N_M"}, "optimization_notes": ["合并了company_basicinfo和compy_basicinfo视图", "使用COALESCE函数统一处理COMPANY_CD字段", "保留了所有字段，包括CSCS_CODE等特有字段", "优化了JOIN条件，移除了多余的括号"], "created_time": "2025-08-01T17:04:37.714390", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_5", "source_views": ["company_core", "compy_core"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_5 AS\nSELECT \n  t.company_global_id AS _C_O_M_P_Y_C_O_R_E_S_I_D,\n  t.company_global_id AS _C_O_M_P_A_N_Y_G_L_O_B_A_L_I_D,\n  a.company_code AS _C_S_C_S_C_O_D_E,\n  t.company_global_id AS _C_O_M_P_A_N_Y_I_D,\n  a.company_name_cn AS _C_O_M_P_A_N_Y_N_M,\n  t.is_list AS IS_LIST,\n  t.list_type AS LIST_TYPE,\n  t.is_new_otc AS IS_NEW_OTC,\n  t.is_bond AS IS_BOND,\n  t.is_finance AS IS_FINANCE,\n  t.finance_type AS FIN_TYPE,\n  t.finance_type_subsidiary AS FIN_TYPE_SUPPV,\n  t.is_it_finance AS IS_ITFIN,\n  t.delete_flag AS SRC_ISDEL,\n  'CSCS' AS SRC_CD,\n  t.delete_flag AS IS_DEL,\n  t.create_dt AS CREATE_DT,\n  t.updt_dt AS UPDT_DT\nFROM cs_info_dw.dwd_company_core t\nJOIN cs_info_dw.dwd_company_basic a \n  ON t.company_global_id = a.company_global_id \n  AND a.delete_flag = 0", "field_mappings": {"COMPY_CORE_SID": "_C_O_M_P_Y_C_O_R_E_S_I_D", "company_global_id": "_C_O_M_P_A_N_Y_G_L_O_B_A_L_I_D", "cscs_code": "_C_S_C_S_C_O_D_E", "COMPANY_ID": "_C_O_M_P_A_N_Y_I_D", "COMPANY_NM": "_C_O_M_P_A_N_Y_N_M"}, "optimization_notes": ["合并了两个视图的共同字段，统一了字段命名", "优化了JOIN条件，去除了冗余的括号", "保留了所有源视图的字段"], "created_time": "2025-08-01T17:06:10.122685", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_6", "source_views": ["company_shareholders_opt", "compy_shareholders_optimized"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_6 AS\nSELECT \n  t.pk_id AS _C_O_M_P_A_N_Y_S_H_A_R_E_H_O_L_D_E_R_S_O_P_T_I_D,\n  t.company_global_id AS _C_O_M_P_A_N_Y_G_L_O_B_A_L_I_D,\n  t.company_global_id AS _C_O_M_P_A_N_Y_I_D,\n  t.shareholder_name AS _S_H_A_R_E_H_O_L_D_E_R_N_A_M_E,\n  t.shareholder_id AS _S_H_A_R_E_H_O_L_D_E_R_G_L_O_B_A_L_I_D,\n  t.shareholder_id AS shareholder_id,\n  t.person_global_id AS PERSON_ID,\n  t.shareholder_type AS SHAREHOLDER_TYPE,\n  STR_TO_DATE(t.report_date,'%Y%m%d') AS RPT_DT,\n  t.shareholder_ratio AS SHAREHOLDER_RATIO,\n  t.subscribed_capital_amount AS SUBSCRIBED_CAPITAL_AMT,\n  STR_TO_DATE(t.subscribed_date,'%Y%m%d') AS SUBSCRIBED_DT,\n  t.subscribed_type AS SUBSCRIBED_TYPE,\n  t.paid_capital_amount AS PAID_CAPITAL_AMT,\n  STR_TO_DATE(t.paid_date,'%Y%m%d') AS PAID_DT,\n  t.paid_type AS PAID_TYPE,\n  t.shareholder_cate AS SHAREHOLDER_CATE,\n  'CSCS' AS SRC_CD,\n  t.delete_flag AS IS_DEL,\n  t.create_dt AS CREATE_DT,\n  t.updt_dt AS UPDT_DT,\n  1100 AS CREATE_BY,\n  1100 AS UPDT_BY,\n  0 AS VERSION\nFROM cs_info_dw.dwd_company_shareholders_opt t", "field_mappings": {"COMPANY_SHAREHOLDERS_OPT_ID": "_C_O_M_P_A_N_Y_S_H_A_R_E_H_O_L_D_E_R_S_O_P_T_I_D", "company_global_id": "_C_O_M_P_A_N_Y_G_L_O_B_A_L_I_D", "company_id": "_C_O_M_P_A_N_Y_I_D", "SHAREHOLDER_NAME": "_S_H_A_R_E_H_O_L_D_E_R_N_A_M_E", "shareholder_global_id": "_S_H_A_R_E_H_O_L_D_E_R_G_L_O_B_A_L_I_D"}, "optimization_notes": ["统一了日期格式处理方式", "合并了两个视图的字段，优先使用更完整的字段集", "标准化了字段命名"], "created_time": "2025-08-01T17:06:10.122685", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_group_7", "source_views": ["dim_security", "dwd_security"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_group_7 AS\nSELECT \n  ds.security_global_id AS _S_E_C_U_R_I_T_Y_G_L_O_B_A_L_I_D,\n  ds.security_cscs_code AS _S_E_C_U_R_I_T_Y_C_S_C_S_C_O_D_E,\n  ds.security_code AS _S_E_C_U_R_I_T_Y_C_O_D_E,\n  ds.security_name AS _S_E_C_U_R_I_T_Y_N_A_M_E,\n  ds.security_short_name AS _S_E_C_U_R_I_T_Y_S_H_O_R_T_N_A_M_E,\n  ds.spell AS SPELL,\n  ds.security_type_id AS SECURITY_TYPE_ID,\n  ds.security_type AS SECURITY_TYPE,\n  ds.trade_market_id AS TRADE_MARKET_ID,\n  ds.market_type AS MARKET_TYPE,\n  ds.company_code AS COMPANY_CODE,\n  ds.company_global_id AS COMPANY_GLOBAL_ID,\n  ds.list_status AS LIST_STATUS,\n  ds.use_status AS USE_STATUS,\n  ds.currency AS CURRENCY,\n  ds.list_date AS LIST_DATE,\n  ds.end_date AS END_DATE,\n  COALESCE(ds.SOURCE_ID, ds.security_global_id) AS SOURCE_ID,\n  COALESCE(ds.SOURCE_CODE, 'CSCS') AS SOURCE_CODE,\n  ds.delete_flag AS DELETE_FLAG,\n  1100 AS CREATE_BY,\n  ds.create_dt AS CREATE_TIME,\n  1100 AS UPDATE_BY,\n  ds.updt_dt AS UPDATE_TIME,\n  0 AS VERSION\nFROM cs_info_dw.dwd_security ds", "field_mappings": {"SECURITY_GLOBAL_ID": "_S_E_C_U_R_I_T_Y_G_L_O_B_A_L_I_D", "SECURITY_CSCS_CODE": "_S_E_C_U_R_I_T_Y_C_S_C_S_C_O_D_E", "SECURITY_CODE": "_S_E_C_U_R_I_T_Y_C_O_D_E", "SECURITY_NAME": "_S_E_C_U_R_I_T_Y_N_A_M_E", "SECURITY_SHORT_NAME": "_S_E_C_U_R_I_T_Y_S_H_O_R_T_N_A_M_E"}, "optimization_notes": ["合并了两个视图的所有字段", "统一了SOURCE_ID和SOURCE_CODE的处理逻辑", "优化了字段命名规范"], "created_time": "2025-08-01T17:06:10.122685", "performance_impact": "批量API生成", "data_completeness": 1.0}, {"merged_view_name": "merged_view_3views", "source_views": ["dim_company_basic", "dim_company_basic_detail", "dwd_company_basic"], "merged_sql": "CREATE OR REPLACE VIEW merged_view_3views AS\nSELECT DISTINCT \n    t.company_global_id AS _C_O_M_P_A_N_Y_G_L_O_B_A_L_I_D,\n    t.company_code AS _C_O_M_P_A_N_Y_C_O_D_E,\n    t.company_name_cn AS _C_O_M_P_A_N_Y_N_A_M_E_C_N,\n    t.company_short_name AS _C_O_M_P_A_N_Y_S_H_O_R_T_N_A_M_E,\n    t.company_name_en AS _C_O_M_P_A_N_Y_N_A_M_E_E_N,\n    t.leg_represent AS _L_E_G_R_E_P_R_E_S_E_N_T,\n    t.chairman AS _C_H_A_I_R_M_A_N,\n    t.general_manager AS _G_E_N_E_R_A_L_M_A_N_A_G_E_R,\n    t.board_secretary AS _B_O_A_R_D_S_E_C_R_E_T_A_R_Y,\n    t.organization_form AS _O_R_G_A_N_I_Z_A_T_I_O_N_F_O_R_M,\n    t.organization_code AS _O_R_G_A_N_I_Z_A_T_I_O_N_C_O_D_E,\n    t.register_capital AS _R_E_G_I_S_T_E_R_C_A_P_I_T_A_L,\n    t.register_capital_currency AS _R_E_G_I_S_T_E_R_C_A_P_I_T_A_L_C_U_R_R_E_N_C_Y,\n    t.register_government AS _R_E_G_I_S_T_E_R_G_O_V_E_R_N_M_E_N_T,\n    t.register_address AS _R_E_G_I_S_T_E_R_A_D_D_R_E_S_S,\n    t.register_country AS _R_E_G_I_S_T_E_R_C_O_U_N_T_R_Y,\n    t.register_region AS _R_E_G_I_S_T_E_R_R_E_G_I_O_N,\n    t.register_city AS _R_E_G_I_S_T_E_R_C_I_T_Y,\n    t.business_license_number AS _B_U_S_I_N_E_S_S_L_I_C_E_N_S_E_N_U_M_B_E_R,\n    t.national_tax_number AS _N_A_T_I_O_N_A_L_T_A_X_N_U_M_B_E_R,\n    t.local_tax_number AS _L_O_C_A_L_T_A_X_N_U_M_B_E_R,\n    t.SOURCE_ID AS _S_O_U_R_C_E_I_D,\n    COALESCE(t.SOURCE_CODE, 'CSCS') AS _S_O_U_R_C_E_C_O_D_E,\n    t.delete_flag AS _D_E_L_E_T_E_F_L_A_G,\n    1100 AS _C_R_E_A_T_E_B_Y,\n    t.create_dt AS _C_R_E_A_T_E_T_I_M_E,\n    1100 AS _U_P_D_A_T_E_B_Y,\n    t.updt_dt AS _U_P_D_A_T_E_T_I_M_E,\n    0 AS _V_E_R_S_I_O_N,\n    t.is_core AS _I_S_C_O_R_E,\n    t.company_state AS _C_O_M_P_A_N_Y_S_T_A_T_U_S,\n    t.register_area AS _R_E_G_I_S_T_E_R_A_R_E_A,\n    t.company_name_spell AS _C_O_M_P_A_N_Y_N_A_M_E_S_P_E_L_L,\n    t.person_global_id AS _P_E_R_S_O_N_G_L_O_B_A_L_I_D,\n    t.found_date AS _F_O_U_N_D_D_A_T_E,\n    t.register_date AS _R_E_G_I_S_T_E_R_D_A_T_E,\n    t.actual_capital AS _A_C_T_U_A_L_C_A_P_I_T_A_L,\n    t.company_address AS _C_O_M_P_A_N_Y_A_D_D_R_E_S_S,\n    t.company_zip_code AS _C_O_M_P_A_N_Y_Z_I_P_C_O_D_E,\n    t.company_phone AS _C_O_M_P_A_N_Y_P_H_O_N_E,\n    t.company_fax AS _C_O_M_P_A_N_Y_F_A_X,\n    t.company_email AS _C_O_M_P_A_N_Y_E_M_A_I_L,\n    t.company_web AS _C_O_M_P_A_N_Y_W_E_B,\n    t.business_scope AS _B_U_S_I_N_E_S_S_S_C_O_P_E,\n    t.main_business AS _M_A_I_N_B_U_S_I_N_E_S_S,\n    t.employ_number AS _E_M_P_L_O_Y_N_U_M_B_E_R,\n    t.info_url AS _I_N_F_O_U_R_L,\n    t.info_news AS _I_N_F_O_N_E_W_S,\n    t.accounting_firm AS _A_C_C_O_U_N_T_I_N_G_F_I_R_M,\n    t.legal_advisor AS _L_E_G_A_L_A_D_V_I_S_O_R,\n    t.company_profile AS _C_O_M_P_A_N_Y_P_R_O_F_I_L_E,\n    t.business_term_start AS _B_U_S_I_N_E_S_S_T_E_R_M_S_T_A_R_T,\n    t.business_term_end AS _B_U_S_I_N_E_S_S_T_E_R_M_E_N_D,\n    t.revoke_date AS _R_E_V_O_K_E_D_A_T_E,\n    t.create_dt AS _C_R_E_A_T_E_D_T,\n    t.updt_dt AS _U_P_D_T_D_T,\n    t.cleans_company_name AS _C_L_E_A_N_S_C_O_M_P_A_N_Y_N_A_M_E,\n    t.origin_organization_form AS _O_R_I_G_I_N_O_R_G_A_N_I_Z_A_T_I_O_N_F_O_R_M,\n    t.status_flag AS _S_T_A_T_U_S_F_L_A_G,\n    t.managing_partner_global_id AS _M_A_N_A_G_I_N_G_P_A_R_T_N_E_R_G_L_O_B_A_L_I_D,\n    t.company_global_id AS _C_O_M_P_A_N_Y_B_A_S_I_C_I_D\nFROM \n    cs_info_dw.dwd_company_basic t", "field_mappings": {"company_global_id": "_C_O_M_P_A_N_Y_G_L_O_B_A_L_I_D", "company_code": "_C_O_M_P_A_N_Y_C_O_D_E", "company_name_cn": "_C_O_M_P_A_N_Y_N_A_M_E_C_N", "company_short_name": "_C_O_M_P_A_N_Y_S_H_O_R_T_N_A_M_E", "company_name_en": "_C_O_M_P_A_N_Y_N_A_M_E_E_N", "leg_represent": "_L_E_G_R_E_P_R_E_S_E_N_T", "chairman": "_C_H_A_I_R_M_A_N", "general_manager": "_G_E_N_E_R_A_L_M_A_N_A_G_E_R", "board_secretary": "_B_O_A_R_D_S_E_C_R_E_T_A_R_Y", "organization_form": "_O_R_G_A_N_I_Z_A_T_I_O_N_F_O_R_M", "organization_code": "_O_R_G_A_N_I_Z_A_T_I_O_N_C_O_D_E", "register_capital": "_R_E_G_I_S_T_E_R_C_A_P_I_T_A_L", "register_capital_currency": "_R_E_G_I_S_T_E_R_C_A_P_I_T_A_L_C_U_R_R_E_N_C_Y", "register_government": "_R_E_G_I_S_T_E_R_G_O_V_E_R_N_M_E_N_T", "register_address": "_R_E_G_I_S_T_E_R_A_D_D_R_E_S_S", "register_country": "_R_E_G_I_S_T_E_R_C_O_U_N_T_R_Y", "register_region": "_R_E_G_I_S_T_E_R_R_E_G_I_O_N", "register_city": "_R_E_G_I_S_T_E_R_C_I_T_Y", "business_license_number": "_B_U_S_I_N_E_S_S_L_I_C_E_N_S_E_N_U_M_B_E_R", "national_tax_number": "_N_A_T_I_O_N_A_L_T_A_X_N_U_M_B_E_R", "local_tax_number": "_L_O_C_A_L_T_A_X_N_U_M_B_E_R", "source_id": "_S_O_U_R_C_E_I_D", "source_code": "_S_O_U_R_C_E_C_O_D_E", "delete_flag": "_D_E_L_E_T_E_F_L_A_G", "create_by": "_C_R_E_A_T_E_B_Y", "create_time": "_C_R_E_A_T_E_T_I_M_E", "update_by": "_U_P_D_A_T_E_B_Y", "update_time": "_U_P_D_A_T_E_T_I_M_E", "version": "_V_E_R_S_I_O_N", "is_core": "_I_S_C_O_R_E", "company_status": "_C_O_M_P_A_N_Y_S_T_A_T_U_S", "register_area": "_R_E_G_I_S_T_E_R_A_R_E_A", "company_name_spell": "_C_O_M_P_A_N_Y_N_A_M_E_S_P_E_L_L", "person_global_id": "_P_E_R_S_O_N_G_L_O_B_A_L_I_D", "found_date": "_F_O_U_N_D_D_A_T_E", "register_date": "_R_E_G_I_S_T_E_R_D_A_T_E", "actual_capital": "_A_C_T_U_A_L_C_A_P_I_T_A_L", "company_address": "_C_O_M_P_A_N_Y_A_D_D_R_E_S_S", "company_zip_code": "_C_O_M_P_A_N_Y_Z_I_P_C_O_D_E", "company_phone": "_C_O_M_P_A_N_Y_P_H_O_N_E", "company_fax": "_C_O_M_P_A_N_Y_F_A_X", "company_email": "_C_O_M_P_A_N_Y_E_M_A_I_L", "company_web": "_C_O_M_P_A_N_Y_W_E_B", "business_scope": "_B_U_S_I_N_E_S_S_S_C_O_P_E", "main_business": "_M_A_I_N_B_U_S_I_N_E_S_S", "employ_number": "_E_M_P_L_O_Y_N_U_M_B_E_R", "info_url": "_I_N_F_O_U_R_L", "info_news": "_I_N_F_O_N_E_W_S", "accounting_firm": "_A_C_C_O_U_N_T_I_N_G_F_I_R_M", "legal_advisor": "_L_E_G_A_L_A_D_V_I_S_O_R", "company_state": "_C_O_M_P_A_N_Y_S_T_A_T_E", "company_profile": "_C_O_M_P_A_N_Y_P_R_O_F_I_L_E", "business_term_start": "_B_U_S_I_N_E_S_S_T_E_R_M_S_T_A_R_T", "business_term_end": "_B_U_S_I_N_E_S_S_T_E_R_M_E_N_D", "revoke_date": "_R_E_V_O_K_E_D_A_T_E", "create_dt": "_C_R_E_A_T_E_D_T", "updt_dt": "_U_P_D_T_D_T", "cleans_company_name": "_C_L_E_A_N_S_C_O_M_P_A_N_Y_N_A_M_E", "origin_organization_form": "_O_R_I_G_I_N_O_R_G_A_N_I_Z_A_T_I_O_N_F_O_R_M", "status_flag": "_S_T_A_T_U_S_F_L_A_G", "managing_partner_global_id": "_M_A_N_A_G_I_N_G_P_A_R_T_N_E_R_G_L_O_B_A_L_I_D", "COMPANY_BASIC_ID": "_C_O_M_P_A_N_Y_B_A_S_I_C_I_D"}, "optimization_notes": ["直接从源表dwd_company_basic获取所有字段，避免视图合并带来的性能开销", "统一了字段命名和表达式，确保数据一致性", "移除了不必要的NULL值和冗余字段", "使用COALESCE函数确保SOURCE_CODE字段有默认值", "使用API生成"], "created_time": "2025-08-01T17:06:11.143513", "performance_impact": "性能优于原视图合并方式，因为直接从单一源表获取数据，避免了视图间的JOIN操作", "data_completeness": 1.0}], "summary": {"total_original_views": 15, "total_merged_views": 7, "reduction_rate": 0.0, "processing_time": "2025-08-01T17:08:38.231262", "total_merge_tasks": 7, "total_field_mappings": 97, "total_optimizations": 24, "average_data_completeness": 1.0, "successful_merges": 7, "failed_merges": 0}}