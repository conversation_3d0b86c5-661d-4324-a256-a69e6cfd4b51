2025-08-04 10:08:06 - ViewAnalysis - INFO - 日志系统初始化完成，日志文件: output/logs\view_analysis_20250804_100806.log
2025-08-04 10:08:06 - ViewAnalysis - INFO - 数据库视图分组程序启动（第二阶段）
2025-08-04 10:08:06 - ViewAnalysis - INFO - 目标：生成 source_group_list 分组列表
2025-08-04 10:08:06 - ViewAnalysis - INFO - ------------------------------------------------------------
2025-08-04 10:08:06 - ViewAnalysis - INFO - ============================================================
2025-08-04 10:08:06 - ViewAnalysis - INFO - 开始第二阶段：视图分组流程
2025-08-04 10:08:06 - ViewAnalysis - INFO - ============================================================
2025-08-04 10:08:06 - ViewAnalysis - INFO - 第一步：加载第一阶段的结果数据
2025-08-04 10:08:06 - ViewAnalysis - INFO - 找到最新的结果文件: output/results\view_table_map_20250804_100801.json
2025-08-04 10:08:06 - ViewAnalysis - INFO - 数据验证通过
2025-08-04 10:08:06 - ViewAnalysis - INFO - 找到最新的结果文件: output/results\view_table_map_20250804_100801.json
2025-08-04 10:08:06 - ViewAnalysis - INFO - 成功加载 50 个视图的映射数据
2025-08-04 10:08:06 - ViewAnalysis - INFO - 输入文件: output/results\view_table_map_20250804_100801.json
2025-08-04 10:08:06 - ViewAnalysis - INFO - 数据统计: {'total_views': 50, 'total_unique_tables': 51, 'total_table_references': 77, 'average_tables_per_view': 1.54, 'table_count_distribution': {1: 31, 3: 3, 2: 14, 4: 1, 5: 1}, 'most_used_tables': [('cs_info_dw.dwd_company_basic', 10), ('cs_info_dw.dim_dictionary_relation', 7), ('cs_info_dw.dwd_bond_basic_info', 4), ('cs_info_dw.dwd_lkp_char_code', 4), ('cs_info_dw.dwd_security', 4), ('cs_info_dw.dwd_bond_pledge', 2), ('cs_info_dw.dwd_bond_warrantor', 2), ('cs_info_dw.dwd_bond_abs_info', 1), ('cs_info_dw.dwd_bond_actcashflow', 1), ('cs_info_dw.dwd_bond_valuation_csi', 1)], 'views_with_no_tables': 0}
2025-08-04 10:08:06 - ViewAnalysis - INFO - 数据统计: {'total_views': 50, 'total_unique_tables': 51, 'total_table_references': 77, 'average_tables_per_view': 1.54, 'table_count_distribution': {1: 31, 3: 3, 2: 14, 4: 1, 5: 1}, 'most_used_tables': [('cs_info_dw.dwd_company_basic', 10), ('cs_info_dw.dim_dictionary_relation', 7), ('cs_info_dw.dwd_bond_basic_info', 4), ('cs_info_dw.dwd_lkp_char_code', 4), ('cs_info_dw.dwd_security', 4), ('cs_info_dw.dwd_bond_pledge', 2), ('cs_info_dw.dwd_bond_warrantor', 2), ('cs_info_dw.dwd_bond_abs_info', 1), ('cs_info_dw.dwd_bond_actcashflow', 1), ('cs_info_dw.dwd_bond_valuation_csi', 1)], 'views_with_no_tables': 0}
2025-08-04 10:08:06 - ViewAnalysis - INFO - 第二步：执行视图分组
2025-08-04 10:08:06 - ViewAnalysis - INFO - 创建完整的分组结果
2025-08-04 10:08:06 - ViewAnalysis - INFO - 完整分组结果创建完成: 48 个分组, 50 个视图
2025-08-04 10:08:06 - ViewAnalysis - INFO - 分组执行完成:
2025-08-04 10:08:06 - ViewAnalysis - INFO -   - 总分组数: 48
2025-08-04 10:08:06 - ViewAnalysis - INFO -   - 总视图数: 50
2025-08-04 10:08:06 - ViewAnalysis - INFO -   - 多视图组: 2
2025-08-04 10:08:06 - ViewAnalysis - INFO -   - 单视图组: 46
2025-08-04 10:08:06 - ViewAnalysis - INFO - 第三步：验证分组结果
2025-08-04 10:08:06 - ViewAnalysis - INFO - 验证分组结果
2025-08-04 10:08:06 - ViewAnalysis - INFO - 分组结果验证通过
2025-08-04 10:08:06 - ViewAnalysis - INFO - 分组结果验证通过
2025-08-04 10:08:06 - ViewAnalysis - INFO - 第四步：保存分组结果
2025-08-04 10:08:06 - ViewAnalysis - INFO - 主要结果已保存: output/stage2_results\source_group_list_20250804_100806.json
2025-08-04 10:08:06 - ViewAnalysis - INFO - 生成综合分析报告
2025-08-04 10:08:06 - ViewAnalysis - INFO - 分析分组分布情况
2025-08-04 10:08:06 - ViewAnalysis - INFO - 分组分布分析完成: {'max_group_size': 2, 'min_group_size': 1, 'average_group_size': 1.04, 'median_group_size': 1.0}
2025-08-04 10:08:06 - ViewAnalysis - INFO - 分析表使用情况
2025-08-04 10:08:06 - ViewAnalysis - INFO - 表使用分析完成: {'total_unique_tables': 51, 'total_unique_combinations': 48, 'average_tables_per_group': 1.52}
2025-08-04 10:08:06 - ViewAnalysis - INFO - 识别潜在的分类候选
2025-08-04 10:08:06 - ViewAnalysis - INFO - 潜在分类分析完成: {'total_duplicate_candidates': 0, 'total_similar_candidates': 1, 'total_complex_groups': 1, 'total_multi_view_groups': 2, 'potential_classification_rate': 2.08}
2025-08-04 10:08:06 - ViewAnalysis - INFO - 综合分析报告生成完成
2025-08-04 10:08:06 - ViewAnalysis - INFO - 分析报告已保存: output/stage2_results\grouping_analysis_20250804_100806.json
2025-08-04 10:08:06 - ViewAnalysis - INFO - 处理日志已保存: output/stage2_results\grouping_log_20250804_100806.json
2025-08-04 10:08:06 - ViewAnalysis - INFO - 详细分组信息已保存: output/stage2_results\detailed_groups_20250804_100806.json
2025-08-04 10:08:06 - ViewAnalysis - INFO - ============================================================
2025-08-04 10:08:06 - ViewAnalysis - INFO - 第二阶段分组流程完成
2025-08-04 10:08:06 - ViewAnalysis - INFO - ============================================================
