=== 视图合并结果 ===

created_time,optimization_notes,合成表,合成表DDL,来源视图1,来源视图1DDL,来源视图2,来源视图2DDL,来源视图3,来源视图3DDL,来源视图4,来源视图4DDL,来源视图5,来源视图5DDL
2025-08-01 16:38:53,合并了bond_pledge和bond_pledge_cm视图，统一了字段命名; 使用ROW_NUMBER()窗口函数替代子查询，提高性能; 通过WHERE条件合并两个视图的数据来源,merged_view_group_2,"CREATE OR REPLACE VIEW merged_view_group_2 AS
SELECT
    `t`.`pk_id` AS `bond_pledge_sid`,
    `t`.`security_global_id` AS `security_global_id`,
    `t`.`security_global_id` AS `secinner_id`,
    `t`.`notice_date` AS `notice_dt`,
    `t`.`pledge_name` AS `pledge_nm`,
    `t`.`pledge_type_id` AS `pledge_type_id`,
    `t`.`pledge_desc` AS `pledge_desc`,
    `t`.`pledge_owner_code` AS `pledge_owner_code`,
    `t`.`pledge_owner` AS `pledge_owner`,
    `t`.`pledge_value` AS `pledge_value`,
    `t`.`priority_value` AS `priority_value`,
    `t`.`pledge_depend_id` AS `pledge_depend_id`,
    `t`.`pledge_control_id` AS `pledge_control_id`,
    `t`.`region` AS `region`,
    `t`.`mitigation_value` AS `mitigation_value`,
    `t`.`delete_flag` AS `src_isdel`,
    IFNULL(`t`.`SOURCE_CODE`,'CSCS') AS `src_cd`,
    `t`.`delete_flag` AS `is_del`,
    `t`.`create_dt` AS `create_dt`,
    `t`.`updt_dt` AS `updt_dt`
FROM `cs_info_dw`.`dwd_bond_pledge` `t`
WHERE `t`.`SOURCE_CODE` = 'MANUAL' OR ROW_NUMBER() OVER (PARTITION BY `t`.`security_global_id`,`t`.`pledge_name` ORDER BY `t`.`updt_dt` DESC,`t`.`create_dt` DESC) = 1",bond_pledge,"CREATE OR REPLACE VIEW bond_pledge AS
SELECT
    `s`.`BOND_PLEDGE_SID` AS `BOND_PLEDGE_SID`,
    `s`.`security_global_id` AS `security_global_id`,
    `s`.`SECINNER_ID` AS `SECINNER_ID`,
    `s`.`NOTICE_DT` AS `NOTICE_DT`,
    `s`.`PLEDGE_NM` AS `PLEDGE_NM`,
    `s`.`PLEDGE_TYPE_ID` AS `PLEDGE_TYPE_ID`,
    `s`.`PLEDGE_DESC` AS `PLEDGE_DESC`,
    `s`.`PLEDGE_OWNER_CODE` AS `PLEDGE_OWNER_CODE`,
    `s`.`PLEDGE_OWNER` AS `PLEDGE_OWNER`,
    `s`.`PLEDGE_VALUE` AS `PLEDGE_VALUE`,
    `s`.`PRIORITY_VALUE` AS `PRIORITY_VALUE`,
    `s`.`PLEDGE_DEPEND_ID` AS `PLEDGE_DEPEND_ID`,
    `s`.`PLEDGE_CONTROL_ID` AS `PLEDGE_CONTROL_ID`,
    `s`.`REGION` AS `REGION`,
    `s`.`MITIGATION_VALUE` AS `MITIGATION_VALUE`,
    `s`.`SRC_ISDEL` AS `SRC_ISDEL`,
    `s`.`SRC_CD` AS `SRC_CD`,
    `s`.`IS_DEL` AS `IS_DEL`,
    `s`.`CREATE_DT` AS `CREATE_DT`,
    `s`.`UPDT_DT` AS `UPDT_DT`
FROM (select `d`.`BOND_PLEDGE_SID` AS `BOND_PLEDGE_SID`,`d`.`security_global_id` AS `security_global_id`,`d`.`SECINNER_ID` AS `SECINNER_ID`,`d`.`NOTICE_DT` AS `NOTICE_DT`,`d`.`PLEDGE_NM` AS `PLEDGE_NM`,`d`.`PLEDGE_TYPE_ID` AS `PLEDGE_TYPE_ID`,`d`.`PLEDGE_DESC` AS `PLEDGE_DESC`,`d`.`PLEDGE_OWNER_CODE` AS `PLEDGE_OWNER_CODE`,`d`.`PLEDGE_OWNER` AS `PLEDGE_OWNER`,`d`.`PLEDGE_VALUE` AS `PLEDGE_VALUE`,`d`.`PRIORITY_VALUE` AS `PRIORITY_VALUE`,`d`.`PLEDGE_DEPEND_ID` AS `PLEDGE_DEPEND_ID`,`d`.`PLEDGE_CONTROL_ID` AS `PLEDGE_CONTROL_ID`,`d`.`REGION` AS `REGION`,`d`.`MITIGATION_VALUE` AS `MITIGATION_VALUE`,`d`.`SRC_ISDEL` AS `SRC_ISDEL`,`d`.`SRC_CD` AS `SRC_CD`,`d`.`IS_DEL` AS `IS_DEL`,`d`.`CREATE_DT` AS `CREATE_DT`,`d`.`UPDT_DT` AS `UPDT_DT`,row_number() OVER (PARTITION BY `d`.`security_global_id`,`d`.`PLEDGE_NM`
ORDER BY `d`.`UPDT_DT` desc,`d`.`CREATE_DT` desc )  AS `RN`
FROM (select `t`.`pk_id` AS `BOND_PLEDGE_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`pledge_name` AS `PLEDGE_NM`,`t`.`pledge_type_id` AS `PLEDGE_TYPE_ID`,`t`.`pledge_desc` AS `PLEDGE_DESC`,`t`.`pledge_owner_code` AS `PLEDGE_OWNER_CODE`,`t`.`pledge_owner` AS `PLEDGE_OWNER`,`t`.`pledge_value` AS `PLEDGE_VALUE`,`t`.`priority_value` AS `PRIORITY_VALUE`,`t`.`pledge_depend_id` AS `PLEDGE_DEPEND_ID`,`t`.`pledge_control_id` AS `PLEDGE_CONTROL_ID`,`t`.`region` AS `REGION`,`t`.`mitigation_value` AS `MITIGATION_VALUE`,`t`.`delete_flag` AS `SRC_ISDEL`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`
FROM `cs_info_dw`.`dwd_bond_pledge` `t`) `d`) `s`
WHERE (`s`.`RN` = 1)",bond_pledge_cm,"CREATE OR REPLACE VIEW bond_pledge_cm AS
SELECT
    `bond_pledge`.`bond_pledge_sid` AS `bond_pledge_sid`,
    `bond_pledge`.`security_global_id` AS `security_global_id`,
    `bond_pledge`.`secinner_id` AS `secinner_id`,
    `bond_pledge`.`notice_dt` AS `notice_dt`,
    `bond_pledge`.`pledge_nm` AS `pledge_nm`,
    `bond_pledge`.`pledge_type_id` AS `pledge_type_id`,
    `bond_pledge`.`pledge_desc` AS `pledge_desc`,
    `bond_pledge`.`pledge_owner_code` AS `pledge_owner_code`,
    `bond_pledge`.`pledge_owner` AS `pledge_owner`,
    `bond_pledge`.`pledge_value` AS `pledge_value`,
    `bond_pledge`.`priority_value` AS `priority_value`,
    `bond_pledge`.`pledge_depend_id` AS `pledge_depend_id`,
    `bond_pledge`.`pledge_control_id` AS `pledge_control_id`,
    `bond_pledge`.`region` AS `region`,
    `bond_pledge`.`mitigation_value` AS `mitigation_value`,
    `bond_pledge`.`src_isdel` AS `src_isdel`,
    `bond_pledge`.`SRC_CD` AS `src_cd`,
    `bond_pledge`.`IS_DEL` AS `is_del`,
    `bond_pledge`.`CREATE_DT` AS `create_dt`,
    `bond_pledge`.`UPDT_DT` AS `updt_dt`
FROM (select `t`.`pk_id` AS `bond_pledge_sid`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `secinner_id`,`t`.`notice_date` AS `notice_dt`,`t`.`pledge_name` AS `pledge_nm`,`t`.`pledge_type_id` AS `pledge_type_id`,`t`.`pledge_desc` AS `pledge_desc`,`t`.`pledge_owner_code` AS `pledge_owner_code`,`t`.`pledge_owner` AS `pledge_owner`,`t`.`pledge_value` AS `pledge_value`,`t`.`priority_value` AS `priority_value`,`t`.`pledge_depend_id` AS `pledge_depend_id`,`t`.`pledge_control_id` AS `pledge_control_id`,`t`.`region` AS `region`,`t`.`mitigation_value` AS `mitigation_value`,`t`.`delete_flag` AS `src_isdel`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`
FROM `cs_info_dw`.`dwd_bond_pledge` `t`
WHERE (`t`.`SOURCE_CODE` = 'MANUAL')) `bond_pledge`",,,,,,
2025-08-01 16:38:53,合并了cfg_dict_mapping_rela和dict_abs_mapping_rela视图; 统一了字段命名和表达式; 合并了所有字段，包括两个视图特有的字段; 优化了查询条件,merged_view_group_3,"CREATE OR REPLACE VIEW merged_view_group_3 AS
SELECT
    `t`.`dictionary_relation_id` AS `dict_mapping_rela_id`,
    `t`.`dictionary_relation` AS `dict_rela_id`,
    `t`.`dictionary_relation_name` AS `dict_rela_name`,
    `t`.`dictionary_relation_code` AS `sec_r_code`,
    `t`.`mapping_relation_code` AS `mapping_r_code`,
    `t`.`order_num` AS `order_num`,
    `t`.`dictionary_relation_rule` AS `dict_rela_rule`,
    `t`.`remark` AS `remark`,
    `t`.`source_code` AS `src_cd`,
    `t`.`create_time` AS `updt_dt`,
    `t`.`delete_flag` AS `is_del`,
    `t`.`create_time` AS `create_dt`,
    0 AS `status`,
    1100 AS `create_by`,
    1100 AS `updt_by`,
    1 AS `version`
FROM `cs_info_dw`.`dim_dictionary_relation` `t`
WHERE `t`.`dictionary_relation` IS NOT NULL",cfg_dict_mapping_rela,"CREATE OR REPLACE VIEW cfg_dict_mapping_rela AS
SELECT
    `t`.`dictionary_relation_id` AS `DICT_MAPPING_RELA_ID`,
    `t`.`dictionary_relation` AS `DICT_RELA_ID`,
    `t`.`dictionary_relation_name` AS `DICT_RELA_NAME`,
    `t`.`dictionary_relation_code` AS `SEC_R_CODE`,
    `t`.`mapping_relation_code` AS `MAPPING_R_CODE`,
    `t`.`order_num` AS `ORDER_NUM`,
    `t`.`dictionary_relation_rule` AS `DICT_RELA_RULE`,
    `t`.`remark` AS `REMARK`,
    `t`.`source_code` AS `SRC_CD`,
    `t`.`create_time` AS `UPDT_DT`,
    `t`.`delete_flag` AS `IS_DEL`,
    `t`.`create_time` AS `CREATE_DT`
FROM `cs_info_dw`.`dim_dictionary_relation` `t`
WHERE (`t`.`dictionary_relation` is not null)",dict_abs_mapping_rela,"CREATE OR REPLACE VIEW dict_abs_mapping_rela AS
SELECT
    `t`.`dictionary_relation_id` AS `DICT_MAPPING_RELA_ID`,
    `t`.`dictionary_relation` AS `DICT_RELA_ID`,
    `t`.`dictionary_relation_name` AS `DICT_RELA_NAME`,
    `t`.`dictionary_relation_code` AS `SEC_R_CODE`,
    `t`.`mapping_relation_code` AS `MAPPING_R_CODE`,
    `t`.`order_num` AS `ORDER_NUM`,
    `t`.`dictionary_relation_rule` AS `DICT_RELA_RULE`,
    `t`.`remark` AS `REMARK`,
    `t`.`source_code` AS `SRC_CD`,
    0 AS `STATUS`,
    `t`.`create_time` AS `CREATE_TIME`,
    `t`.`update_time` AS `UPDT_DT`,
    `t`.`delete_flag` AS `IS_DEL`,
    `t`.`create_time` AS `CREATE_DT`,
    1100 AS `create_by`,
    1100 AS `UPDT_BY`,
    1 AS `VERSION`
FROM `cs_info_dw`.`dim_dictionary_relation` `t`
WHERE (`t`.`dictionary_relation` is not null)",,,,,,
2025-08-01 16:38:53,合并了company_basicinfo和compy_basicinfo视图; 使用COALESCE函数统一处理company_cd字段; 优化了JOIN条件，消除冗余; 保留了所有字段，统一了命名,merged_view_group_4,"CREATE OR REPLACE VIEW merged_view_group_4 AS
SELECT
    `t`.`company_global_id` AS `compy_basicinfo_sid`,
    `t`.`company_global_id` AS `company_global_id`,
    `t`.`company_global_id` AS `company_id`,
    COALESCE(`t`.`company_code`, LPAD(`t`.`company_global_id`, 15, 0)) AS `company_cd`,
    `t`.`company_name_cn` AS `company_nm`,
    `t`.`company_short_name` AS `company_snm`,
    `t`.`cleans_company_name` AS `clens_company_nm`,
    `t`.`company_name_en` AS `fen_nm`,
    `t`.`leg_represent` AS `leg_represent`,
    `t`.`chairman` AS `chairman`,
    `t`.`general_manager` AS `gmanager`,
    `t`.`board_secretary` AS `bsecretary`,
    `t`.`organization_form` AS `org_form_id`,
    `t`.`found_date` AS `found_dt`,
    `t`.`register_capital_currency` AS `currency`,
    `t`.`register_capital` AS `reg_capital`,
    `t`.`register_country` AS `country`,
    `t`.`register_region` AS `region`,
    `t`.`register_city` AS `city`,
    `t`.`register_address` AS `reg_addr`,
    `t`.`company_address` AS `office_addr`,
    `t`.`company_zip_code` AS `office_post_cd`,
    `t`.`company_phone` AS `company_ph`,
    `t`.`company_fax` AS `company_fax`,
    `t`.`company_email` AS `company_em`,
    `t`.`company_web` AS `company_web`,
    `t`.`business_scope` AS `busin_scope`,
    `t`.`main_business` AS `main_busin`,
    `t`.`employ_number` AS `employ_num`,
    `t`.`business_license_number` AS `blnumb`,
    `t`.`national_tax_number` AS `ntrnum`,
    `t`.`local_tax_number` AS `ltrnum`,
    `t`.`organization_code` AS `orgnum`,
    `t`.`register_date` AS `reg_dt`,
    `t`.`info_url` AS `info_url`,
    `t`.`info_news` AS `info_news`,
    `t`.`accounting_firm` AS `accounting_firm`,
    `t`.`legal_advisor` AS `legal_advisor`,
    `t`.`company_state` AS `company_st`,
    `t`.`company_profile` AS `company_profile`,
    NULL AS `src_company_cd`,
    IFNULL(`t`.`SOURCE_CODE`,'CSCS') AS `src_cd`,
    `t`.`delete_flag` AS `is_del`,
    `t`.`create_dt` AS `create_dt`,
    `t`.`updt_dt` AS `updt_dt`,
    `t`.`is_core` AS `is_core`,
    `t`.`actual_capital` AS `actual_capital`,
    `t`.`business_term_start` AS `start_dt`,
    `t`.`business_term_end` AS `end_dt`,
    IFNULL(`a`.`dictionary_name`,`t`.`company_state`) AS `orig_company_st`,
    IFNULL(`b`.`dictionary_name`,`t`.`organization_form`) AS `orig_org_form`,
    `t`.`register_government` AS `reg_gov`,
    `t`.`revoke_date` AS `revoke_dt`,
    NULL AS `src_updt_dt`
FROM `cs_info_dw`.`dwd_company_basic` `t`
    LEFT JOIN `cs_info_dw`.`dim_dictionary_info` `a` ON `a`.`dictionary_type` = 'COMPY_BASICINFO.COMPANY_ST' AND `t`.`company_state` = `a`.`dictionary_code` AND `a`.`delete_flag` = 0
    LEFT JOIN `cs_info_dw`.`dim_dictionary_info` `b` ON `b`.`dictionary_type` = 'COMPY_ALL_INFO.ORG_FORM_ID' AND `t`.`organization_form` = `b`.`dictionary_code` AND `b`.`delete_flag` = 0
WHERE `t`.`delete_flag` = 0",company_basicinfo,"CREATE OR REPLACE VIEW company_basicinfo AS
SELECT
    `t`.`company_global_id` AS `COMPY_BASICINFO_SID`,
    `t`.`company_global_id` AS `company_global_id`,
    `t`.`company_global_id` AS `COMPANY_ID`,
    lpad(`t`.`company_global_id`,15,0) AS `COMPANY_CD`,
    `t`.`company_name_cn` AS `COMPANY_NM`,
    `t`.`company_short_name` AS `COMPANY_SNM`,
    `t`.`cleans_company_name` AS `CLENS_COMPANY_NM`,
    `t`.`company_name_en` AS `FEN_NM`,
    `t`.`leg_represent` AS `LEG_REPRESENT`,
    `t`.`chairman` AS `CHAIRMAN`,
    `t`.`general_manager` AS `GMANAGER`,
    `t`.`board_secretary` AS `BSECRETARY`,
    `t`.`organization_form` AS `ORG_FORM_ID`,
    `t`.`found_date` AS `FOUND_DT`,
    `t`.`register_capital_currency` AS `CURRENCY`,
    `t`.`register_capital` AS `REG_CAPITAL`,
    `t`.`register_country` AS `COUNTRY`,
    `t`.`register_region` AS `REGION`,
    `t`.`register_city` AS `CITY`,
    `t`.`register_address` AS `REG_ADDR`,
    `t`.`company_address` AS `OFFICE_ADDR`,
    `t`.`company_zip_code` AS `OFFICE_POST_CD`,
    `t`.`company_phone` AS `COMPANY_PH`,
    `t`.`company_fax` AS `COMPANY_FAX`,
    `t`.`company_email` AS `COMPANY_EM`,
    `t`.`company_web` AS `COMPANY_WEB`,
    `t`.`business_scope` AS `BUSIN_SCOPE`,
    `t`.`main_business` AS `MAIN_BUSIN`,
    `t`.`employ_number` AS `EMPLOY_NUM`,
    `t`.`business_license_number` AS `BLNUMB`,
    `t`.`national_tax_number` AS `NTRNUM`,
    `t`.`local_tax_number` AS `LTRNUM`,
    `t`.`organization_code` AS `ORGNUM`,
    `t`.`register_date` AS `REG_DT`,
    `t`.`info_url` AS `INFO_URL`,
    `t`.`info_news` AS `INFO_NEWS`,
    `t`.`accounting_firm` AS `ACCOUNTING_FIRM`,
    `t`.`legal_advisor` AS `LEGAL_ADVISOR`,
    `t`.`company_state` AS `COMPANY_ST`,
    `t`.`company_profile` AS `COMPANY_PROFILE`,
    NULL AS `SRC_COMPANY_CD`,
    ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,
    `t`.`delete_flag` AS `IS_DEL`,
    `t`.`create_dt` AS `CREATE_DT`,
    `t`.`updt_dt` AS `UPDT_DT`,
    `t`.`is_core` AS `IS_CORE`,
    `t`.`actual_capital` AS `ACTUAL_CAPITAL`,
    `t`.`business_term_start` AS `START_DT`,
    `t`.`business_term_end` AS `END_DT`,
    ifnull(`a`.`dictionary_name`,`t`.`company_state`) AS `ORIG_COMPANY_ST`,
    ifnull(`b`.`dictionary_name`,`t`.`organization_form`) AS `ORIG_ORG_FORM`,
    `t`.`register_government` AS `REG_GOV`,
    `t`.`revoke_date` AS `REVOKE_DT`,
    NULL AS `SRC_UPDT_DT`
FROM ((`cs_info_dw`.`dwd_company_basic` `t` left join `cs_info_dw`.`dim_dictionary_info` `a` on(((`a`.`dictionary_type` = 'COMPY_BASICINFO.COMPANY_ST') and (`t`.`company_state` = `a`.`dictionary_code`) and (`a`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_info` `b` on(((`b`.`dictionary_type` = 'COMPY_ALL_INFO.ORG_FORM_ID') and (`t`.`organization_form` = `b`.`dictionary_code`) and (`b`.`delete_flag` = 0))))
WHERE (`t`.`delete_flag` = 0)",compy_basicinfo,"CREATE OR REPLACE VIEW compy_basicinfo AS
SELECT
    `t`.`company_global_id` AS `COMPY_BASICINFO_SID`,
    `t`.`company_code` AS `CSCS_CODE`,
    `t`.`company_global_id` AS `COMPANY_ID`,
    `t`.`company_code` AS `COMPANY_CD`,
    `t`.`company_name_cn` AS `COMPANY_NM`,
    `t`.`company_short_name` AS `COMPANY_SNM`,
    `t`.`cleans_company_name` AS `CLENS_COMPANY_NM`,
    `t`.`company_name_en` AS `FEN_NM`,
    `t`.`leg_represent` AS `LEG_REPRESENT`,
    `t`.`chairman` AS `CHAIRMAN`,
    `t`.`general_manager` AS `GMANAGER`,
    `t`.`board_secretary` AS `BSECRETARY`,
    `t`.`organization_form` AS `ORG_FORM_ID`,
    `t`.`found_date` AS `FOUND_DT`,
    `t`.`register_capital_currency` AS `CURRENCY`,
    `t`.`register_capital` AS `REG_CAPITAL`,
    `t`.`register_country` AS `COUNTRY`,
    `t`.`register_region` AS `REGION`,
    `t`.`register_city` AS `CITY`,
    `t`.`register_address` AS `REG_ADDR`,
    `t`.`company_address` AS `OFFICE_ADDR`,
    `t`.`company_zip_code` AS `OFFICE_POST_CD`,
    `t`.`company_phone` AS `COMPANY_PH`,
    `t`.`company_fax` AS `COMPANY_FAX`,
    `t`.`company_email` AS `COMPANY_EM`,
    `t`.`company_web` AS `COMPANY_WEB`,
    `t`.`business_scope` AS `BUSIN_SCOPE`,
    `t`.`main_business` AS `MAIN_BUSIN`,
    `t`.`employ_number` AS `EMPLOY_NUM`,
    `t`.`business_license_number` AS `BLNUMB`,
    `t`.`national_tax_number` AS `NTRNUM`,
    `t`.`local_tax_number` AS `LTRNUM`,
    `t`.`organization_code` AS `ORGNUM`,
    `t`.`register_date` AS `REG_DT`,
    `t`.`info_url` AS `INFO_URL`,
    `t`.`info_news` AS `INFO_NEWS`,
    `t`.`accounting_firm` AS `ACCOUNTING_FIRM`,
    `t`.`legal_advisor` AS `LEGAL_ADVISOR`,
    `t`.`company_state` AS `COMPANY_ST`,
    `t`.`company_profile` AS `COMPANY_PROFILE`,
    NULL AS `SRC_COMPANY_CD`,
    ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,
    `t`.`delete_flag` AS `IS_DEL`,
    `t`.`create_dt` AS `CREATE_DT`,
    `t`.`updt_dt` AS `UPDT_DT`,
    `t`.`is_core` AS `IS_CORE`,
    `t`.`actual_capital` AS `ACTUAL_CAPITAL`,
    `t`.`business_term_start` AS `START_DT`,
    `t`.`business_term_end` AS `END_DT`,
    ifnull(`a`.`dictionary_name`,`t`.`company_state`) AS `ORIG_COMPANY_ST`,
    ifnull(`b`.`dictionary_name`,`t`.`organization_form`) AS `ORIG_ORG_FORM`,
    `t`.`register_government` AS `REG_GOV`,
    `t`.`revoke_date` AS `REVOKE_DT`,
    NULL AS `SRC_UPDT_DT`,
    `t`.`company_global_id` AS `COMPANY_GLOBAL_ID`
FROM ((`cs_info_dw`.`dwd_company_basic` `t` left join `cs_info_dw`.`dim_dictionary_info` `a` on(((`a`.`dictionary_type` = 'COMPY_BASICINFO.COMPANY_ST') and (`t`.`company_state` = `a`.`dictionary_code`) and (`a`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_info` `b` on(((`b`.`dictionary_type` = 'COMPY_ALL_INFO.ORG_FORM_ID') and (`t`.`organization_form` = `b`.`dictionary_code`) and (`b`.`delete_flag` = 0))))",,,,,,
2025-08-01 16:38:53,统一了字段命名规范，使用下划线命名法; 合并了两个视图的查询逻辑，避免重复JOIN; 保留了所有源视图的字段,merged_view_group_5,"CREATE OR REPLACE VIEW merged_view_group_5 AS
SELECT
    t.company_global_id AS compy_core_sid,
    t.company_global_id AS company_global_id,
    a.company_code AS cscs_code,
    t.company_global_id AS company_id,
    a.company_name_cn AS company_nm,
    t.is_list AS is_list,
    t.list_type AS list_type,
    t.is_new_otc AS is_new_otc,
    t.is_bond AS is_bond,
    t.is_finance AS is_finance,
    t.finance_type AS fin_type,
    t.finance_type_subsidiary AS fin_type_suppv,
    t.is_it_finance AS is_itfin,
    t.delete_flag AS src_isdel,
    'CSCS' AS src_cd,
    t.delete_flag AS is_del,
    t.create_dt AS create_dt,
    t.updt_dt AS updt_dt
FROM cs_info_dw.dwd_company_core t
    JOIN cs_info_dw.dwd_company_basic a ON t.company_global_id = a.company_global_id AND a.delete_flag = 0",company_core,"CREATE OR REPLACE VIEW company_core AS
SELECT
    `t`.`company_global_id` AS `COMPY_CORE_SID`,
    `t`.`company_global_id` AS `company_global_id`,
    `a`.`company_code` AS `cscs_code`,
    `t`.`company_global_id` AS `COMPANY_ID`,
    `a`.`company_name_cn` AS `COMPANY_NM`,
    `t`.`is_list` AS `IS_LIST`,
    `t`.`list_type` AS `LIST_TYPE`,
    `t`.`is_new_otc` AS `IS_NEW_OTC`,
    `t`.`is_bond` AS `IS_BOND`,
    `t`.`is_finance` AS `IS_FINANCE`,
    `t`.`finance_type` AS `FIN_TYPE`,
    `t`.`finance_type_subsidiary` AS `FIN_TYPE_SUPPV`,
    `t`.`is_it_finance` AS `IS_ITFIN`,
    `t`.`delete_flag` AS `SRC_ISDEL`,
    'CSCS' AS `SRC_CD`,
    `t`.`delete_flag` AS `IS_DEL`,
    `t`.`create_dt` AS `CREATE_DT`,
    `t`.`updt_dt` AS `UPDT_DT`
FROM (`cs_info_dw`.`dwd_company_core` `t` join `cs_info_dw`.`dwd_company_basic` `a` on(((`t`.`company_global_id` = `a`.`company_global_id`) and (`a`.`delete_flag` = 0))))",compy_core,"CREATE OR REPLACE VIEW compy_core AS
SELECT
    `a`.`company_global_id` AS `COMPANY_ID`,
    `a`.`company_name_cn` AS `COMPANY_NM`,
    `t`.`is_list` AS `IS_LIST`,
    `t`.`list_type` AS `LIST_TYPE`,
    `t`.`is_new_otc` AS `IS_NEW_OTC`,
    `t`.`is_bond` AS `IS_BOND`,
    `t`.`is_finance` AS `IS_FINANCE`,
    `t`.`finance_type` AS `FIN_TYPE`,
    `t`.`finance_type_subsidiary` AS `FIN_TYPE_SUPP`,
    `t`.`is_it_finance` AS `IS_ITFIN`,
    `t`.`delete_flag` AS `IS_DEL`,
    `t`.`create_dt` AS `CREATE_DT`,
    `t`.`updt_dt` AS `UPDT_DT`
FROM (`cs_info_dw`.`dwd_company_core` `t` join `cs_info_dw`.`dwd_company_basic` `a` on(((`t`.`company_global_id` = `a`.`company_global_id`) and (`a`.`delete_flag` = 0))))",,,,,,
2025-08-01 16:38:53,统一了日期字段的处理方式; 合并了两个视图的所有字段; 标准化了字段命名; 保留了所有源数据,merged_view_group_6,"CREATE OR REPLACE VIEW merged_view_group_6 AS
SELECT
    t.pk_id AS company_shareholders_opt_id,
    t.company_global_id AS company_global_id,
    t.company_global_id AS company_id,
    t.shareholder_name AS shareholder_name,
    t.shareholder_id AS shareholder_global_id,
    t.shareholder_id AS shareholder_id,
    t.person_global_id AS person_id,
    t.shareholder_type AS shareholder_type,
    STR_TO_DATE(t.report_date, '%Y%m%d') AS rpt_dt,
    t.shareholder_ratio AS shareholder_ratio,
    t.subscribed_capital_amount AS subscribed_capital_amt,
    STR_TO_DATE(t.subscribed_date, '%Y%m%d') AS subscribed_dt,
    t.subscribed_type AS subscribed_type,
    t.paid_capital_amount AS paid_capital_amt,
    STR_TO_DATE(t.paid_date, '%Y%m%d') AS paid_dt,
    t.paid_type AS paid_type,
    t.shareholder_cate AS shareholder_cate,
    'CSCS' AS src_cd,
    t.delete_flag AS is_del,
    t.create_dt AS create_dt,
    t.updt_dt AS updt_dt,
    NULL AS cscs_code,
    t.shareholder_company_code AS sharehd_code,
    0 AS status,
    1100 AS create_by,
    1100 AS updt_by,
    0 AS version,
    t.person_global_id AS person_global_id
FROM cs_info_dw.dwd_company_shareholders_opt t",company_shareholders_opt,"CREATE OR REPLACE VIEW company_shareholders_opt AS
SELECT
    `t`.`pk_id` AS `COMPANY_SHAREHOLDERS_OPT_ID`,
    `t`.`company_global_id` AS `company_global_id`,
    `t`.`company_global_id` AS `company_id`,
    `t`.`shareholder_name` AS `SHAREHOLDER_NAME`,
    `t`.`shareholder_id` AS `shareholder_global_id`,
    `t`.`shareholder_id` AS `shareholder_id`,
    `t`.`person_global_id` AS `PERSON_ID`,
    `t`.`shareholder_type` AS `SHAREHOLDER_TYPE`,
    str_to_date(`t`.`report_date`,'%Y%m%d') AS `STR_TO_DATE(T.REPORT_DATE,__STR_1__)`,
    `t`.`shareholder_ratio` AS `SHAREHOLDER_RATIO`,
    `t`.`subscribed_capital_amount` AS `SUBSCRIBED_CAPITAL_AMOUNT`,
    str_to_date(`t`.`subscribed_date`,'%Y%m%d') AS `STR_TO_DATE(T.SUBSCRIBED_DATE,__STR_3__)`,
    `t`.`subscribed_type` AS `SUBSCRIBED_TYPE`,
    `t`.`paid_capital_amount` AS `PAID_CAPITAL_AMOUNT`,
    str_to_date(`t`.`paid_date`,'%Y%m%d') AS `PAID_DATE`,
    `t`.`paid_type` AS `PAID_TYPE`,
    `t`.`shareholder_cate` AS `SHAREHOLDER_CATE`,
    'CSCS' AS `SRC_CD`,
    `t`.`delete_flag` AS `IS_DEL`,
    `t`.`create_dt` AS `CREATE_DT`,
    `t`.`updt_dt` AS `UPDT_DT`
FROM `cs_info_dw`.`dwd_company_shareholders_opt` `t`",compy_shareholders_optimized,"CREATE OR REPLACE VIEW compy_shareholders_optimized AS
SELECT
    `cs_info_dw`.`dwd_company_shareholders_opt`.`pk_id` AS `COMPY_SHAREHOLDERS_OPT_SID`,
    NULL AS `CSCS_CODE`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`company_global_id` AS `COMPANY_ID`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`shareholder_name` AS `SHAREHD_NAME`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`shareholder_company_code` AS `SHAREHD_CODE`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`shareholder_id` AS `SHAREHD_ID`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`person_global_id` AS `PERSON_ID`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`shareholder_type` AS `SHAREHD_TYPE`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`report_date` AS `RPT_DT`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`shareholder_ratio` AS `SHAREHD_RATIO`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`subscribed_capital_amount` AS `SUBSCRIBED_CAPITAL_AMT`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`subscribed_date` AS `SUBSCRIBED_DT`,
    'SUBSCRIBED_TYPE' AS `SUBSCRIBED_TYPE`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`paid_capital_amount` AS `PAID_CAPITAL_AMT`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`paid_date` AS `PAID_DT`,
    'PAID_TYPE' AS `PAID_TYPE`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`shareholder_cate` AS `SHAREHD_CATE`,
    NULL AS `SRC_ID`,
    'CSCS' AS `SRC_CD`,
    0 AS `STATUS`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`delete_flag` AS `IS_DEL`,
    1100 AS `CREATE_BY`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`create_dt` AS `CREATE_DT`,
    1100 AS `UPDT_BY`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`updt_dt` AS `UPDT_DT`,
    0 AS `VERSION`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`company_global_id` AS `COMPANY_GLOBAL_ID`,
    NULL AS `SHAREHD_GLOBAL_ID`,
    `cs_info_dw`.`dwd_company_shareholders_opt`.`person_global_id` AS `PERSON_GLOBAL_ID`
FROM `cs_info_dw`.`dwd_company_shareholders_opt`",,,,,,
2025-08-01 16:38:53,统一了SOURCE_ID和SOURCE_CODE的处理逻辑; 合并了两个视图的所有字段; 标准化了字段命名; 优化了COALESCE函数的使用,merged_view_group_7,"CREATE OR REPLACE VIEW merged_view_group_7 AS
SELECT
    ds.security_global_id AS security_global_id,
    ds.security_cscs_code AS security_cscs_code,
    ds.security_code AS security_code,
    ds.security_name AS security_name,
    ds.security_short_name AS security_short_name,
    ds.spell AS spell,
    ds.security_type_id AS security_type_id,
    ds.security_type AS security_type,
    ds.trade_market_id AS trade_market_id,
    ds.market_type AS market_type,
    ds.company_code AS company_code,
    ds.company_global_id AS company_global_id,
    ds.list_status AS list_status,
    ds.use_status AS use_status,
    ds.currency AS currency,
    ds.list_date AS list_date,
    ds.end_date AS end_date,
    COALESCE(ds.SOURCE_ID, ds.security_global_id) AS source_id,
    COALESCE(ds.SOURCE_CODE, 'CSCS') AS source_code,
    ds.delete_flag AS delete_flag,
    1100 AS create_by,
    ds.create_dt AS create_time,
    1100 AS update_by,
    ds.updt_dt AS update_time,
    0 AS version,
    ds.security_global_id AS security_id,
    ds.status_flag AS status_flag
FROM cs_info_dw.dwd_security ds",dim_security,"CREATE OR REPLACE VIEW dim_security AS
SELECT
    `ds`.`security_global_id` AS `SECURITY_GLOBAL_ID`,
    `ds`.`security_cscs_code` AS `SECURITY_CSCS_CODE`,
    `ds`.`security_code` AS `SECURITY_CODE`,
    `ds`.`security_name` AS `SECURITY_NAME`,
    `ds`.`security_short_name` AS `SECURITY_SHORT_NAME`,
    `ds`.`spell` AS `SPELL`,
    `ds`.`security_type_id` AS `SECURITY_TYPE_ID`,
    `ds`.`security_type` AS `SECURITY_TYPE`,
    `ds`.`trade_market_id` AS `TRADE_MARKET_ID`,
    `ds`.`market_type` AS `MARKET_TYPE`,
    `ds`.`company_code` AS `COMPANY_CODE`,
    `ds`.`company_global_id` AS `COMPANY_GLOBAL_ID`,
    `ds`.`list_status` AS `LIST_STATUS`,
    `ds`.`use_status` AS `USE_STATUS`,
    `ds`.`currency` AS `CURRENCY`,
    `ds`.`list_date` AS `LIST_DATE`,
    `ds`.`end_date` AS `END_DATE`,
    `ds`.`security_global_id` AS `SOURCE_ID`,
    'CSCS' AS `SOURCE_CODE`,
    `ds`.`delete_flag` AS `DELETE_FLAG`,
    1100 AS `CREATE_BY`,
    `ds`.`create_dt` AS `CREATE_TIME`,
    1100 AS `UPDATE_BY`,
    `ds`.`updt_dt` AS `UPDATE_TIME`,
    0 AS `VERSION`
FROM `cs_info_dw`.`dwd_security` `ds`",dwd_security,"CREATE OR REPLACE VIEW dwd_security AS
SELECT
    `cs_info_dw`.`dwd_security`.`security_global_id` AS `security_global_id`,
    `cs_info_dw`.`dwd_security`.`security_cscs_code` AS `security_cscs_code`,
    `cs_info_dw`.`dwd_security`.`security_code` AS `security_code`,
    `cs_info_dw`.`dwd_security`.`security_name` AS `security_name`,
    `cs_info_dw`.`dwd_security`.`security_short_name` AS `security_short_name`,
    `cs_info_dw`.`dwd_security`.`spell` AS `spell`,
    `cs_info_dw`.`dwd_security`.`security_type_id` AS `security_type_id`,
    `cs_info_dw`.`dwd_security`.`security_type` AS `security_type`,
    `cs_info_dw`.`dwd_security`.`trade_market_id` AS `trade_market_id`,
    `cs_info_dw`.`dwd_security`.`market_type` AS `market_type`,
    `cs_info_dw`.`dwd_security`.`company_code` AS `company_code`,
    `cs_info_dw`.`dwd_security`.`company_global_id` AS `company_global_id`,
    `cs_info_dw`.`dwd_security`.`list_status` AS `list_status`,
    `cs_info_dw`.`dwd_security`.`use_status` AS `use_status`,
    `cs_info_dw`.`dwd_security`.`currency` AS `currency`,
    `cs_info_dw`.`dwd_security`.`delete_flag` AS `delete_flag`,
    `cs_info_dw`.`dwd_security`.`create_dt` AS `create_dt`,
    `cs_info_dw`.`dwd_security`.`updt_dt` AS `updt_dt`,
    `cs_info_dw`.`dwd_security`.`list_date` AS `list_date`,
    `cs_info_dw`.`dwd_security`.`end_date` AS `end_date`,
    `cs_info_dw`.`dwd_security`.`status_flag` AS `status_flag`,
    coalesce(`cs_info_dw`.`dwd_security`.`SOURCE_ID`,`cs_info_dw`.`dwd_security`.`security_global_id`) AS `SOURCE_ID`,
    coalesce(`cs_info_dw`.`dwd_security`.`SOURCE_CODE`,'CSCS') AS `SOURCE_CODE`,
    `cs_info_dw`.`dwd_security`.`security_global_id` AS `SECURITY_ID`,
    1100 AS `CREATE_BY`,
    `cs_info_dw`.`dwd_security`.`create_dt` AS `CREATE_TIME`,
    1100 AS `UPDATE_BY`,
    `cs_info_dw`.`dwd_security`.`updt_dt` AS `UPDATE_TIME`,
    0 AS `VERSION`
FROM `cs_info_dw`.`dwd_security`",,,,,,
2025-08-01 16:38:53,统一了所有视图的字段来源，直接从基础表获取数据; 使用COALESCE函数处理可能为NULL的字段，确保数据完整性; 消除了冗余的JOIN操作，直接从单一来源表获取数据; 使用API生成,merged_view_3views,"CREATE OR REPLACE VIEW merged_view_3views AS
SELECT
    t.company_global_id AS company_global_id,
    t.company_code AS company_code,
    t.company_name_cn AS company_name_cn,
    t.company_short_name AS company_short_name,
    t.company_name_en AS company_name_en,
    t.leg_represent AS leg_represent,
    t.chairman AS chairman,
    t.general_manager AS general_manager,
    t.board_secretary AS board_secretary,
    t.organization_form AS organization_form,
    t.organization_code AS organization_code,
    t.register_capital AS register_capital,
    t.register_capital_currency AS register_capital_currency,
    t.register_government AS register_government,
    t.register_address AS register_address,
    t.register_country AS register_country,
    t.register_region AS register_region,
    t.register_city AS register_city,
    t.business_license_number AS business_license_number,
    t.national_tax_number AS national_tax_number,
    t.local_tax_number AS local_tax_number,
    COALESCE(t.source_id, NULL) AS source_id,
    COALESCE(t.source_code, 'CSCS') AS source_code,
    t.delete_flag AS delete_flag,
    COALESCE(t.create_by, 1100) AS create_by,
    COALESCE(t.create_time, t.create_dt) AS create_time,
    COALESCE(t.update_by, 1100) AS update_by,
    COALESCE(t.update_time, t.updt_dt) AS update_time,
    t.version AS version,
    t.is_core AS is_core,
    t.company_state AS company_status,
    t.register_area AS register_area,
    t.company_name_spell AS company_name_spell,
    t.person_global_id AS person_global_id,
    t.found_date AS found_date,
    t.register_date AS register_date,
    t.actual_capital AS actual_capital,
    t.company_address AS company_address,
    t.company_zip_code AS company_zip_code,
    t.company_phone AS company_phone,
    t.company_fax AS company_fax,
    t.company_email AS company_email,
    t.company_web AS company_web,
    t.business_scope AS business_scope,
    t.main_business AS main_business,
    t.employ_number AS employ_number,
    t.info_url AS info_url,
    t.info_news AS info_news,
    t.accounting_firm AS accounting_firm,
    t.legal_advisor AS legal_advisor,
    t.company_state AS company_state,
    t.company_profile AS company_profile,
    t.business_term_start AS business_term_start,
    t.business_term_end AS business_term_end,
    t.revoke_date AS revoke_date,
    t.cleans_company_name AS cleans_company_name,
    t.origin_organization_form AS origin_organization_form,
    t.status_flag AS status_flag,
    t.managing_partner_global_id AS managing_partner_global_id,
    t.company_global_id AS company_basic_id
FROM
    cs_info_dw.dwd_company_basic t",dim_company_basic,"CREATE OR REPLACE VIEW dim_company_basic AS
SELECT
    `t`.`company_global_id` AS `company_global_id`,
    `t`.`company_code` AS `company_code`,
    `t`.`company_name_cn` AS `company_name_cn`,
    `t`.`company_short_name` AS `company_short_name`,
    `t`.`company_name_en` AS `company_name_en`,
    `t`.`leg_represent` AS `leg_represent`,
    `t`.`chairman` AS `chairman`,
    `t`.`general_manager` AS `general_manager`,
    `t`.`board_secretary` AS `board_secretary`,
    `t`.`organization_form` AS `organization_form`,
    `t`.`organization_code` AS `organization_code`,
    `t`.`register_capital` AS `register_capital`,
    `t`.`register_capital_currency` AS `register_capital_currency`,
    `t`.`register_government` AS `register_government`,
    `t`.`register_address` AS `register_address`,
    `t`.`register_country` AS `register_country`,
    `t`.`register_region` AS `register_region`,
    `t`.`register_city` AS `register_city`,
    `t`.`business_license_number` AS `business_license_number`,
    `t`.`national_tax_number` AS `national_tax_number`,
    `t`.`local_tax_number` AS `local_tax_number`,
    NULL AS `source_id`,
    'CSCS' AS `source_code`,
    `t`.`delete_flag` AS `delete_flag`,
    1100 AS `create_by`,
    `t`.`create_dt` AS `create_time`,
    NULL AS `update_by`,
    `t`.`updt_dt` AS `update_time`,
    0 AS `version`,
    `t`.`is_core` AS `is_core`,
    `t`.`company_state` AS `company_status`,
    `t`.`register_area` AS `register_area`,
    `t`.`company_name_spell` AS `company_name_spell`,
    `t`.`person_global_id` AS `person_global_id`
FROM `cs_info_dw`.`dwd_company_basic` `t`",dim_company_basic_detail,"CREATE OR REPLACE VIEW dim_company_basic_detail AS
SELECT
    `t`.`company_global_id` AS `company_global_id`,
    `t`.`found_date` AS `found_date`,
    `t`.`register_date` AS `register_date`,
    `t`.`actual_capital` AS `actual_capital`,
    `t`.`company_address` AS `company_address`,
    `t`.`company_zip_code` AS `company_zip_code`,
    `t`.`company_phone` AS `company_phone`,
    `t`.`company_fax` AS `company_fax`,
    `t`.`company_email` AS `company_email`,
    `t`.`company_web` AS `company_web`,
    `t`.`business_scope` AS `business_scope`,
    `t`.`main_business` AS `main_business`,
    `t`.`employ_number` AS `employ_number`,
    `t`.`info_url` AS `info_url`,
    `t`.`info_news` AS `info_news`,
    `t`.`accounting_firm` AS `accounting_firm`,
    `t`.`legal_advisor` AS `legal_advisor`,
    `t`.`company_state` AS `company_state`,
    `t`.`company_profile` AS `company_profile`,
    `t`.`business_term_start` AS `business_term_start`,
    `t`.`business_term_end` AS `business_term_end`,
    `t`.`revoke_date` AS `revoke_date`,
    `t`.`SOURCE_ID` AS `source_id`,
    `t`.`SOURCE_CODE` AS `source_code`,
    `t`.`delete_flag` AS `delete_flag`,
    NULL AS `create_by`,
    `t`.`create_dt` AS `create_time`,
    NULL AS `update_by`,
    `t`.`updt_dt` AS `update_time`,
    0 AS `version`
FROM `cs_info_dw`.`dwd_company_basic` `t`",dwd_company_basic,"CREATE OR REPLACE VIEW dwd_company_basic AS
SELECT
    `cs_info_dw`.`dwd_company_basic`.`company_global_id` AS `company_global_id`,
    `cs_info_dw`.`dwd_company_basic`.`company_code` AS `company_code`,
    `cs_info_dw`.`dwd_company_basic`.`company_name_cn` AS `company_name_cn`,
    `cs_info_dw`.`dwd_company_basic`.`company_short_name` AS `company_short_name`,
    `cs_info_dw`.`dwd_company_basic`.`company_name_en` AS `company_name_en`,
    `cs_info_dw`.`dwd_company_basic`.`leg_represent` AS `leg_represent`,
    `cs_info_dw`.`dwd_company_basic`.`chairman` AS `chairman`,
    `cs_info_dw`.`dwd_company_basic`.`general_manager` AS `general_manager`,
    `cs_info_dw`.`dwd_company_basic`.`board_secretary` AS `board_secretary`,
    `cs_info_dw`.`dwd_company_basic`.`organization_form` AS `organization_form`,
    `cs_info_dw`.`dwd_company_basic`.`organization_code` AS `organization_code`,
    `cs_info_dw`.`dwd_company_basic`.`register_capital` AS `register_capital`,
    `cs_info_dw`.`dwd_company_basic`.`register_capital_currency` AS `register_capital_currency`,
    `cs_info_dw`.`dwd_company_basic`.`register_government` AS `register_government`,
    `cs_info_dw`.`dwd_company_basic`.`register_address` AS `register_address`,
    `cs_info_dw`.`dwd_company_basic`.`register_country` AS `register_country`,
    `cs_info_dw`.`dwd_company_basic`.`register_region` AS `register_region`,
    `cs_info_dw`.`dwd_company_basic`.`register_city` AS `register_city`,
    `cs_info_dw`.`dwd_company_basic`.`business_license_number` AS `business_license_number`,
    `cs_info_dw`.`dwd_company_basic`.`national_tax_number` AS `national_tax_number`,
    `cs_info_dw`.`dwd_company_basic`.`local_tax_number` AS `local_tax_number`,
    `cs_info_dw`.`dwd_company_basic`.`found_date` AS `found_date`,
    `cs_info_dw`.`dwd_company_basic`.`register_date` AS `register_date`,
    `cs_info_dw`.`dwd_company_basic`.`actual_capital` AS `actual_capital`,
    `cs_info_dw`.`dwd_company_basic`.`company_address` AS `company_address`,
    `cs_info_dw`.`dwd_company_basic`.`company_zip_code` AS `company_zip_code`,
    `cs_info_dw`.`dwd_company_basic`.`company_phone` AS `company_phone`,
    `cs_info_dw`.`dwd_company_basic`.`company_fax` AS `company_fax`,
    `cs_info_dw`.`dwd_company_basic`.`company_email` AS `company_email`,
    `cs_info_dw`.`dwd_company_basic`.`company_web` AS `company_web`,
    `cs_info_dw`.`dwd_company_basic`.`main_business` AS `main_business`,
    `cs_info_dw`.`dwd_company_basic`.`employ_number` AS `employ_number`,
    `cs_info_dw`.`dwd_company_basic`.`info_url` AS `info_url`,
    `cs_info_dw`.`dwd_company_basic`.`info_news` AS `info_news`,
    `cs_info_dw`.`dwd_company_basic`.`accounting_firm` AS `accounting_firm`,
    `cs_info_dw`.`dwd_company_basic`.`legal_advisor` AS `legal_advisor`,
    `cs_info_dw`.`dwd_company_basic`.`company_state` AS `company_state`,
    `cs_info_dw`.`dwd_company_basic`.`company_profile` AS `company_profile`,
    `cs_info_dw`.`dwd_company_basic`.`business_term_start` AS `business_term_start`,
    `cs_info_dw`.`dwd_company_basic`.`business_term_end` AS `business_term_end`,
    `cs_info_dw`.`dwd_company_basic`.`revoke_date` AS `revoke_date`,
    `cs_info_dw`.`dwd_company_basic`.`delete_flag` AS `delete_flag`,
    `cs_info_dw`.`dwd_company_basic`.`create_dt` AS `create_dt`,
    `cs_info_dw`.`dwd_company_basic`.`updt_dt` AS `updt_dt`,
    `cs_info_dw`.`dwd_company_basic`.`is_core` AS `is_core`,
    `cs_info_dw`.`dwd_company_basic`.`cleans_company_name` AS `cleans_company_name`,
    `cs_info_dw`.`dwd_company_basic`.`business_scope` AS `business_scope`,
    `cs_info_dw`.`dwd_company_basic`.`person_global_id` AS `person_global_id`,
    `cs_info_dw`.`dwd_company_basic`.`origin_organization_form` AS `origin_organization_form`,
    `cs_info_dw`.`dwd_company_basic`.`status_flag` AS `status_flag`,
    `cs_info_dw`.`dwd_company_basic`.`managing_partner_global_id` AS `managing_partner_global_id`,
    `cs_info_dw`.`dwd_company_basic`.`register_area` AS `register_area`,
    `cs_info_dw`.`dwd_company_basic`.`company_name_spell` AS `company_name_spell`,
    `cs_info_dw`.`dwd_company_basic`.`SOURCE_ID` AS `source_id`,
    coalesce(`cs_info_dw`.`dwd_company_basic`.`SOURCE_CODE`,'CSCS') AS `SOURCE_CODE`,
    `cs_info_dw`.`dwd_company_basic`.`company_global_id` AS `COMPANY_BASIC_ID`,
    1100 AS `CREATE_BY`,
    `cs_info_dw`.`dwd_company_basic`.`create_dt` AS `CREATE_TIME`,
    1100 AS `UPDATE_BY`,
    `cs_info_dw`.`dwd_company_basic`.`updt_dt` AS `UPDATE_TIME`,
    0 AS `version`
FROM `cs_info_dw`.`dwd_company_basic`",,,,

=== 重复视图分析 ===

重复视图1,重复视图1DDL,重复视图2,重复视图2DDL
bond_basicinfo,"CREATE OR REPLACE VIEW bond_basicinfo AS
SELECT
    `bond_basicinfo`.`security_global_id` AS `security_global_id`,
    `bond_basicinfo`.`secinner_id` AS `secinner_id`,
    `bond_basicinfo`.`security_cscs_code` AS `security_cscs_code`,
    `bond_basicinfo`.`company_global_id` AS `company_global_id`,
    `bond_basicinfo`.`company_id` AS `company_id`,
    `bond_basicinfo`.`security_cd` AS `security_cd`,
    `bond_basicinfo`.`security_nm` AS `security_nm`,
    `bond_basicinfo`.`security_snm` AS `security_snm`,
    `bond_basicinfo`.`spell` AS `spell`,
    `bond_basicinfo`.`security_type_id` AS `security_type_id`,
    `bond_basicinfo`.`issue_year` AS `issue_year`,
    `bond_basicinfo`.`issue_num` AS `issue_num`,
    `bond_basicinfo`.`currency` AS `currency`,
    `bond_basicinfo`.`trade_market_id` AS `trade_market_id`,
    `bond_basicinfo`.`notice_dt` AS `notice_dt`,
    `bond_basicinfo`.`src_create_dt` AS `src_create_dt`,
    `bond_basicinfo`.`public_dt` AS `public_dt`,
    `bond_basicinfo`.`public_announce_dt` AS `public_announce_dt`,
    `bond_basicinfo`.`issue_dt` AS `issue_dt`,
    `bond_basicinfo`.`frst_value_dt` AS `frst_value_dt`,
    `bond_basicinfo`.`last_value_dt` AS `last_value_dt`,
    `bond_basicinfo`.`puttable_dt` AS `puttable_dt`,
    `bond_basicinfo`.`mrty_dt` AS `mrty_dt`,
    `bond_basicinfo`.`payment_dt` AS `payment_dt`,
    `bond_basicinfo`.`redem_dt` AS `redem_dt`,
    `bond_basicinfo`.`delist_dt` AS `delist_dt`,
    `bond_basicinfo`.`pay_day` AS `pay_day`,
    `bond_basicinfo`.`bond_type1_id` AS `bond_type1_id`,
    `bond_basicinfo`.`bond_type2_id` AS `bond_type2_id`,
    `bond_basicinfo`.`bond_form_id` AS `bond_form_id`,
    `bond_basicinfo`.`other_nature` AS `other_nature`,
    `bond_basicinfo`.`credit_rating` AS `credit_rating`,
    `bond_basicinfo`.`issue_vol` AS `issue_vol`,
    `bond_basicinfo`.`listvolsz` AS `listvolsz`,
    `bond_basicinfo`.`listvolsh` AS `listvolsh`,
    `bond_basicinfo`.`bond_period` AS `bond_period`,
    `bond_basicinfo`.`par_value` AS `par_value`,
    `bond_basicinfo`.`issue_price` AS `issue_price`,
    `bond_basicinfo`.`coupon_type_cd` AS `coupon_type_cd`,
    `bond_basicinfo`.`pay_type_cd` AS `pay_type_cd`,
    `bond_basicinfo`.`pay_desc` AS `pay_desc`,
    `bond_basicinfo`.`coupon_rule_cd` AS `coupon_rule_cd`,
    `bond_basicinfo`.`payment_type_cd` AS `payment_type_cd`,
    `bond_basicinfo`.`coupon_rate` AS `coupon_rate`,
    `bond_basicinfo`.`floor_rate` AS `floor_rate`,
    `bond_basicinfo`.`bnchmk_spread` AS `bnchmk_spread`,
    `bond_basicinfo`.`bnchmk_id` AS `bnchmk_id`,
    `bond_basicinfo`.`rate_desc` AS `rate_desc`,
    `bond_basicinfo`.`pay_peryear` AS `pay_peryear`,
    `bond_basicinfo`.`add_rate` AS `add_rate`,
    `bond_basicinfo`.`puttable_price` AS `puttable_price`,
    `bond_basicinfo`.`expect_rate` AS `expect_rate`,
    `bond_basicinfo`.`issue_type_cd` AS `issue_type_cd`,
    `bond_basicinfo`.`refe_rate` AS `refe_rate`,
    `bond_basicinfo`.`add_issue_num` AS `add_issue_num`,
    `bond_basicinfo`.`is_cross` AS `is_cross`,
    `bond_basicinfo`.`is_floor_rate` AS `is_floor_rate`,
    `bond_basicinfo`.`is_adjust_type` AS `is_adjust_type`,
    `bond_basicinfo`.`is_redem` AS `is_redem`,
    `bond_basicinfo`.`is_plit_debt` AS `is_plit_debt`,
    `bond_basicinfo`.`is_puttable` AS `is_puttable`,
    `bond_basicinfo`.`is_change` AS `is_change`,
    `bond_basicinfo`.`fwd_rate` AS `fwd_rate`,
    `bond_basicinfo`.`redem_price` AS `redem_price`,
    `bond_basicinfo`.`swaps_cd` AS `swaps_cd`,
    `bond_basicinfo`.`tax_rate` AS `tax_rate`,
    `bond_basicinfo`.`coupon_style_cd` AS `coupon_style_cd`,
    `bond_basicinfo`.`Option_Termdes` AS `option_termdes`,
    `bond_basicinfo`.`Base_Asset` AS `base_asset`,
    `bond_basicinfo`.`coupon_method_cd` AS `coupon_method_cd`,
    `bond_basicinfo`.`bond_option` AS `bond_option`,
    `bond_basicinfo`.`credit_typein_id` AS `credit_typein_id`,
    `bond_basicinfo`.`credit_typeout_id` AS `credit_typeout_id`,
    `bond_basicinfo`.`remark` AS `remark`,
    `bond_basicinfo`.`src_portfolio_cd` AS `src_portfolio_cd`,
    `bond_basicinfo`.`src_isdel` AS `src_isdel`,
    `bond_basicinfo`.`SRC_CD` AS `src_cd`,
    `bond_basicinfo`.`IS_DEL` AS `is_del`,
    `bond_basicinfo`.`CREATE_DT` AS `create_dt`,
    `bond_basicinfo`.`UPDT_DT` AS `updt_dt`,
    `bond_basicinfo`.`is_abs` AS `is_abs`,
    `bond_basicinfo`.`is_sub` AS `is_sub`,
    `bond_basicinfo`.`is_continuous` AS `is_continuous`
FROM (select `a`.`security_global_id` AS `security_global_id`,`a`.`security_global_id` AS `secinner_id`,`a`.`security_cscs_code` AS `security_cscs_code`,`a`.`company_global_id` AS `company_global_id`,`a`.`company_global_id` AS `company_id`,`a`.`security_code` AS `security_cd`,`a`.`security_name` AS `security_nm`,`a`.`security_short_name` AS `security_snm`,`a`.`spell` AS `spell`,ifnull(`mp`.`dictionary_relation_code`,`a`.`security_type_id`) AS `security_type_id`,`a`.`issue_year` AS `issue_year`,`a`.`issue_num` AS `issue_num`,ifnull(`c2`.`dictionary_relation_code`,`a`.`currency`) AS `currency`,ifnull(`c3`.`dictionary_relation_code`,`a`.`trade_market_id`) AS `trade_market_id`,`a`.`notice_date` AS `notice_dt`,`a`.`create_date` AS `src_create_dt`,`a`.`public_date` AS `public_dt`,`a`.`public_announce_date` AS `public_announce_dt`,`a`.`issue_date` AS `issue_dt`,ifnull(`a`.`first_value_date`,`a`.`issue_date`) AS `frst_value_dt`,`a`.`last_value_date` AS `last_value_dt`,`a`.`put_table_date` AS `puttable_dt`,`a`.`maturity_date` AS `mrty_dt`,`a`.`payment_date` AS `payment_dt`,`a`.`redeem_date` AS `redem_dt`,`a`.`delist_date` AS `delist_dt`,`a`.`pay_day` AS `pay_day`,`mp`.`dictionary_relation_code` AS `bond_type1_id`,`mp`.`dictionary_relation_code` AS `bond_type2_id`,`a`.`bond_form_id` AS `bond_form_id`,`a`.`other_nature` AS `other_nature`,`a`.`credit_rating` AS `credit_rating`,`a`.`issue_vol` AS `issue_vol`,`a`.`list_vol_sz` AS `listvolsz`,`a`.`list_vol_sh` AS `listvolsh`,`a`.`bond_period` AS `bond_period`,`a`.`par_value` AS `par_value`,`a`.`issue_price` AS `issue_price`,`a`.`coupon_type_code` AS `coupon_type_cd`,`a`.`pay_type_code` AS `pay_type_cd`,`a`.`pay_desc` AS `pay_desc`,`a`.`coupon_rule_code` AS `coupon_rule_cd`,`a`.`payment_type_code` AS `payment_type_cd`,`a`.`coupon_rate` AS `coupon_rate`,`a`.`floor_rate` AS `floor_rate`,`a`.`bnchmk_spread` AS `bnchmk_spread`,`a`.`bnchmk_id` AS `bnchmk_id`,`a`.`rate_desc` AS `rate_desc`,`a`.`pay_per_year` AS `pay_peryear`,`a`.`add_rate` AS `add_rate`,`a`.`put_table_price` AS `puttable_price`,`a`.`expect_rate` AS `expect_rate`,`a`.`issue_type_code` AS `issue_type_cd`,`a`.`reference_rate` AS `refe_rate`,`a`.`add_issue_num` AS `add_issue_num`,`a`.`is_cross` AS `is_cross`,`a`.`is_floor_rate` AS `is_floor_rate`,`a`.`is_adjust_type` AS `is_adjust_type`,`a`.`is_redeem` AS `is_redem`,`a`.`is_plit_debt` AS `is_plit_debt`,`a`.`is_put_table` AS `is_puttable`,`a`.`is_change` AS `is_change`,`a`.`fwd_rate` AS `fwd_rate`,`a`.`redeem_price` AS `redem_price`,`a`.`swaps_code` AS `swaps_cd`,`a`.`tax_rate` AS `tax_rate`,`a`.`coupon_style_code` AS `coupon_style_cd`,`a`.`option_termdes` AS `Option_Termdes`,`a`.`base_asset` AS `Base_Asset`,`a`.`coupon_method_code` AS `coupon_method_cd`,`a`.`bond_option` AS `bond_option`,`a`.`credit_type_in_id` AS `credit_typein_id`,`a`.`credit_type_out_id` AS `credit_typeout_id`,`a`.`remark` AS `remark`,`a`.`src_portfolio_cd` AS `src_portfolio_cd`,`a`.`delete_flag` AS `src_isdel`,ifnull(`a`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`a`.`delete_flag` AS `IS_DEL`,`a`.`create_dt` AS `CREATE_DT`,`a`.`updt_dt` AS `UPDT_DT`,(case when (ifnull(`mp`.`mapping_relation_code`,`mq`.`mapping_relation_code`) like '060007%') then 1 else 0 end) AS `is_abs`,`a`.`is_sub` AS `is_sub`,(case when (`a`.`option_termdes` like '%+N') then 1 else 0 end) AS `is_continuous`
FROM ((((((`cs_info_dw`.`dwd_bond_basic_info` `a` left join (select `n`.`dictionary_relation_code` AS `dictionary_relation_code`,`n`.`mapping_relation_code` AS `mapping_relation_code`,`n`.`source_code` AS `source_code`,`n`.`delete_flag` AS `delete_flag`,`n`.`dictionary_relation` AS `dictionary_relation`
FROM (select `p`.`dictionary_relation_code` AS `dictionary_relation_code`,`p`.`mapping_relation_code` AS `mapping_relation_code`,`p`.`source_code` AS `source_code`,`p`.`delete_flag` AS `delete_flag`,`p`.`dictionary_relation` AS `dictionary_relation`,row_number() OVER (PARTITION BY `p`.`dictionary_relation_code`
ORDER BY `p`.`dictionary_relation_id` )  AS `RN`
FROM `cs_info_dw`.`dim_dictionary_relation` `p`
WHERE ((`p`.`dictionary_relation` = 'SECURITY_TYPE') and (`p`.`source_code` = 'CSCS'))) `n`
WHERE (`n`.`RN` = 1)) `mq` on(((`mq`.`dictionary_relation` = 'SECURITY_TYPE') and (`a`.`security_type_id` = `mq`.`dictionary_relation_code`) and (`mq`.`source_code` = 'CSCS') and (`mq`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `dlcc` on(((`a`.`security_type_id` = cast(`dlcc`.`constant_id` as char charset utf8mb4)) and (`dlcc`.`constant_type` = 201) and (`dlcc`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `mp` on(((`mp`.`dictionary_relation` = 'SECURITY_TYPE') and (`dlcc`.`constant_code` = `mp`.`mapping_relation_code`) and (`mp`.`source_code` = 'CSCS') and (`mp`.`delete_flag` = 0) and (((`a`.`issue_type_code` = 1) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010201)) or ((`a`.`issue_type_code` = 2) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010202)) or ((`dlcc`.`constant_code` <> '060005007') and (1 = 1)))))) left join `cs_info_dw`.`dim_dictionary_relation` `c2` on(((`a`.`currency` = `c2`.`mapping_relation_code`) and (`c2`.`dictionary_relation` = 'COMPY_BASICINFO.CURRENCY') and (`c2`.`source_code` = 'CSCS.MDS') and (`c2`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `c` on(((`c`.`constant_id` = `a`.`trade_market_id`) and (`c`.`constant_type` = 206) and (`c`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `c3` on(((`c`.`constant_code` = `c3`.`mapping_relation_code`) and (`c3`.`dictionary_relation` = 'MARKET_TYPE') and (`c3`.`source_code` = 'CSCS') and (`c3`.`delete_flag` = 0))))
WHERE (`a`.`delete_flag` = 0)) `bond_basicinfo`",bond_basicinfo_dw,"CREATE OR REPLACE VIEW bond_basicinfo_dw AS
SELECT
    `bond_basicinfo`.`security_global_id` AS `security_global_id`,
    `bond_basicinfo`.`secinner_id` AS `secinner_id`,
    `bond_basicinfo`.`security_cscs_code` AS `security_cscs_code`,
    `bond_basicinfo`.`company_global_id` AS `company_global_id`,
    `bond_basicinfo`.`company_id` AS `company_id`,
    `bond_basicinfo`.`security_cd` AS `security_cd`,
    `bond_basicinfo`.`security_nm` AS `security_nm`,
    `bond_basicinfo`.`security_snm` AS `security_snm`,
    `bond_basicinfo`.`spell` AS `spell`,
    `bond_basicinfo`.`security_type_id` AS `security_type_id`,
    `bond_basicinfo`.`issue_year` AS `issue_year`,
    `bond_basicinfo`.`issue_num` AS `issue_num`,
    `bond_basicinfo`.`currency` AS `currency`,
    `bond_basicinfo`.`trade_market_id` AS `trade_market_id`,
    `bond_basicinfo`.`notice_dt` AS `notice_dt`,
    `bond_basicinfo`.`src_create_dt` AS `src_create_dt`,
    `bond_basicinfo`.`public_dt` AS `public_dt`,
    `bond_basicinfo`.`public_announce_dt` AS `public_announce_dt`,
    `bond_basicinfo`.`issue_dt` AS `issue_dt`,
    `bond_basicinfo`.`frst_value_dt` AS `frst_value_dt`,
    `bond_basicinfo`.`last_value_dt` AS `last_value_dt`,
    `bond_basicinfo`.`puttable_dt` AS `puttable_dt`,
    `bond_basicinfo`.`mrty_dt` AS `mrty_dt`,
    `bond_basicinfo`.`payment_dt` AS `payment_dt`,
    `bond_basicinfo`.`redem_dt` AS `redem_dt`,
    `bond_basicinfo`.`delist_dt` AS `delist_dt`,
    `bond_basicinfo`.`pay_day` AS `pay_day`,
    `bond_basicinfo`.`bond_type1_id` AS `bond_type1_id`,
    `bond_basicinfo`.`bond_type2_id` AS `bond_type2_id`,
    `bond_basicinfo`.`bond_form_id` AS `bond_form_id`,
    `bond_basicinfo`.`other_nature` AS `other_nature`,
    `bond_basicinfo`.`credit_rating` AS `credit_rating`,
    `bond_basicinfo`.`issue_vol` AS `issue_vol`,
    `bond_basicinfo`.`listvolsz` AS `listvolsz`,
    `bond_basicinfo`.`listvolsh` AS `listvolsh`,
    `bond_basicinfo`.`bond_period` AS `bond_period`,
    `bond_basicinfo`.`par_value` AS `par_value`,
    `bond_basicinfo`.`issue_price` AS `issue_price`,
    `bond_basicinfo`.`coupon_type_cd` AS `coupon_type_cd`,
    `bond_basicinfo`.`pay_type_cd` AS `pay_type_cd`,
    `bond_basicinfo`.`pay_desc` AS `pay_desc`,
    `bond_basicinfo`.`coupon_rule_cd` AS `coupon_rule_cd`,
    `bond_basicinfo`.`payment_type_cd` AS `payment_type_cd`,
    `bond_basicinfo`.`coupon_rate` AS `coupon_rate`,
    `bond_basicinfo`.`floor_rate` AS `floor_rate`,
    `bond_basicinfo`.`bnchmk_spread` AS `bnchmk_spread`,
    `bond_basicinfo`.`bnchmk_id` AS `bnchmk_id`,
    `bond_basicinfo`.`rate_desc` AS `rate_desc`,
    `bond_basicinfo`.`pay_peryear` AS `pay_peryear`,
    `bond_basicinfo`.`add_rate` AS `add_rate`,
    `bond_basicinfo`.`puttable_price` AS `puttable_price`,
    `bond_basicinfo`.`expect_rate` AS `expect_rate`,
    `bond_basicinfo`.`issue_type_cd` AS `issue_type_cd`,
    `bond_basicinfo`.`refe_rate` AS `refe_rate`,
    `bond_basicinfo`.`add_issue_num` AS `add_issue_num`,
    `bond_basicinfo`.`is_cross` AS `is_cross`,
    `bond_basicinfo`.`is_floor_rate` AS `is_floor_rate`,
    `bond_basicinfo`.`is_adjust_type` AS `is_adjust_type`,
    `bond_basicinfo`.`is_redem` AS `is_redem`,
    `bond_basicinfo`.`is_plit_debt` AS `is_plit_debt`,
    `bond_basicinfo`.`is_puttable` AS `is_puttable`,
    `bond_basicinfo`.`is_change` AS `is_change`,
    `bond_basicinfo`.`fwd_rate` AS `fwd_rate`,
    `bond_basicinfo`.`redem_price` AS `redem_price`,
    `bond_basicinfo`.`swaps_cd` AS `swaps_cd`,
    `bond_basicinfo`.`tax_rate` AS `tax_rate`,
    `bond_basicinfo`.`coupon_style_cd` AS `coupon_style_cd`,
    `bond_basicinfo`.`Option_Termdes` AS `option_termdes`,
    `bond_basicinfo`.`Base_Asset` AS `base_asset`,
    `bond_basicinfo`.`coupon_method_cd` AS `coupon_method_cd`,
    `bond_basicinfo`.`bond_option` AS `bond_option`,
    `bond_basicinfo`.`credit_typein_id` AS `credit_typein_id`,
    `bond_basicinfo`.`credit_typeout_id` AS `credit_typeout_id`,
    `bond_basicinfo`.`remark` AS `remark`,
    `bond_basicinfo`.`src_portfolio_cd` AS `src_portfolio_cd`,
    `bond_basicinfo`.`src_isdel` AS `src_isdel`,
    `bond_basicinfo`.`SRC_CD` AS `src_cd`,
    `bond_basicinfo`.`IS_DEL` AS `is_del`,
    `bond_basicinfo`.`CREATE_DT` AS `create_dt`,
    `bond_basicinfo`.`UPDT_DT` AS `updt_dt`,
    `bond_basicinfo`.`is_abs` AS `is_abs`,
    `bond_basicinfo`.`is_sub` AS `is_sub`
FROM (select `a`.`security_global_id` AS `security_global_id`,`a`.`security_global_id` AS `secinner_id`,`a`.`security_cscs_code` AS `security_cscs_code`,`a`.`company_global_id` AS `company_global_id`,`a`.`company_global_id` AS `company_id`,`a`.`security_code` AS `security_cd`,`a`.`security_name` AS `security_nm`,`a`.`security_short_name` AS `security_snm`,`a`.`spell` AS `spell`,ifnull(`mp`.`dictionary_relation_code`,`a`.`security_type_id`) AS `security_type_id`,`a`.`issue_year` AS `issue_year`,`a`.`issue_num` AS `issue_num`,ifnull(`c2`.`dictionary_relation_code`,`a`.`currency`) AS `currency`,ifnull(`c3`.`dictionary_relation_code`,`a`.`trade_market_id`) AS `trade_market_id`,`a`.`notice_date` AS `notice_dt`,`a`.`create_date` AS `src_create_dt`,`a`.`public_date` AS `public_dt`,`a`.`public_announce_date` AS `public_announce_dt`,`a`.`issue_date` AS `issue_dt`,ifnull(`a`.`first_value_date`,`a`.`issue_date`) AS `frst_value_dt`,`a`.`last_value_date` AS `last_value_dt`,`a`.`put_table_date` AS `puttable_dt`,`a`.`maturity_date` AS `mrty_dt`,`a`.`payment_date` AS `payment_dt`,`a`.`redeem_date` AS `redem_dt`,`a`.`delist_date` AS `delist_dt`,`a`.`pay_day` AS `pay_day`,`mp`.`dictionary_relation_code` AS `bond_type1_id`,`mp`.`dictionary_relation_code` AS `bond_type2_id`,`a`.`bond_form_id` AS `bond_form_id`,`a`.`other_nature` AS `other_nature`,`a`.`credit_rating` AS `credit_rating`,`a`.`issue_vol` AS `issue_vol`,`a`.`list_vol_sz` AS `listvolsz`,`a`.`list_vol_sh` AS `listvolsh`,`a`.`bond_period` AS `bond_period`,`a`.`par_value` AS `par_value`,`a`.`issue_price` AS `issue_price`,`a`.`coupon_type_code` AS `coupon_type_cd`,`a`.`pay_type_code` AS `pay_type_cd`,`a`.`pay_desc` AS `pay_desc`,`a`.`coupon_rule_code` AS `coupon_rule_cd`,`a`.`payment_type_code` AS `payment_type_cd`,`a`.`coupon_rate` AS `coupon_rate`,`a`.`floor_rate` AS `floor_rate`,`a`.`bnchmk_spread` AS `bnchmk_spread`,`a`.`bnchmk_id` AS `bnchmk_id`,`a`.`rate_desc` AS `rate_desc`,`a`.`pay_per_year` AS `pay_peryear`,`a`.`add_rate` AS `add_rate`,`a`.`put_table_price` AS `puttable_price`,`a`.`expect_rate` AS `expect_rate`,`a`.`issue_type_code` AS `issue_type_cd`,`a`.`reference_rate` AS `refe_rate`,`a`.`add_issue_num` AS `add_issue_num`,`a`.`is_cross` AS `is_cross`,`a`.`is_floor_rate` AS `is_floor_rate`,`a`.`is_adjust_type` AS `is_adjust_type`,`a`.`is_redeem` AS `is_redem`,`a`.`is_plit_debt` AS `is_plit_debt`,`a`.`is_put_table` AS `is_puttable`,`a`.`is_change` AS `is_change`,`a`.`fwd_rate` AS `fwd_rate`,`a`.`redeem_price` AS `redem_price`,`a`.`swaps_code` AS `swaps_cd`,`a`.`tax_rate` AS `tax_rate`,`a`.`coupon_style_code` AS `coupon_style_cd`,`a`.`option_termdes` AS `Option_Termdes`,`a`.`base_asset` AS `Base_Asset`,`a`.`coupon_method_code` AS `coupon_method_cd`,`a`.`bond_option` AS `bond_option`,`a`.`credit_type_in_id` AS `credit_typein_id`,`a`.`credit_type_out_id` AS `credit_typeout_id`,`a`.`remark` AS `remark`,`a`.`src_portfolio_cd` AS `src_portfolio_cd`,`a`.`delete_flag` AS `src_isdel`,ifnull(`a`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`a`.`delete_flag` AS `IS_DEL`,`a`.`create_dt` AS `CREATE_DT`,`a`.`updt_dt` AS `UPDT_DT`,(case when (ifnull(`mp`.`mapping_relation_code`,`mq`.`mapping_relation_code`) like '060007%') then 1 else 0 end) AS `is_abs`,`a`.`is_sub` AS `is_sub`
FROM ((((((`cs_info_dw`.`dwd_bond_basic_info` `a` left join (select `n`.`dictionary_relation_code` AS `dictionary_relation_code`,`n`.`mapping_relation_code` AS `mapping_relation_code`,`n`.`source_code` AS `source_code`,`n`.`delete_flag` AS `delete_flag`,`n`.`dictionary_relation` AS `dictionary_relation`
FROM (select `p`.`dictionary_relation_code` AS `dictionary_relation_code`,`p`.`mapping_relation_code` AS `mapping_relation_code`,`p`.`source_code` AS `source_code`,`p`.`delete_flag` AS `delete_flag`,`p`.`dictionary_relation` AS `dictionary_relation`,row_number() OVER (PARTITION BY `p`.`dictionary_relation_code`
ORDER BY `p`.`dictionary_relation_id` )  AS `RN`
FROM `cs_info_dw`.`dim_dictionary_relation` `p`
WHERE ((`p`.`dictionary_relation` = 'SECURITY_TYPE') and (`p`.`source_code` = 'CSCS'))) `n`
WHERE (`n`.`RN` = 1)) `mq` on(((`mq`.`dictionary_relation` = 'SECURITY_TYPE') and (`a`.`security_type_id` = `mq`.`dictionary_relation_code`) and (`mq`.`source_code` = 'CSCS') and (`mq`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `dlcc` on(((`a`.`security_type_id` = cast(`dlcc`.`constant_id` as char charset utf8mb4)) and (`dlcc`.`constant_type` = 201) and (`dlcc`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `mp` on(((`mp`.`dictionary_relation` = 'SECURITY_TYPE') and (`dlcc`.`constant_code` = `mp`.`mapping_relation_code`) and (`mp`.`source_code` = 'CSCS') and (`mp`.`delete_flag` = 0) and (((`a`.`issue_type_code` = 1) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010201)) or ((`a`.`issue_type_code` = 2) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010202)) or ((`dlcc`.`constant_code` <> '060005007') and (1 = 1)))))) left join `cs_info_dw`.`dim_dictionary_relation` `c2` on(((`a`.`currency` = `c2`.`mapping_relation_code`) and (`c2`.`dictionary_relation` = 'COMPY_BASICINFO.CURRENCY') and (`c2`.`source_code` = 'CSCS.MDS') and (`c2`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `c` on(((`c`.`constant_id` = `a`.`trade_market_id`) and (`c`.`constant_type` = 206) and (`c`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `c3` on(((`c`.`constant_code` = `c3`.`mapping_relation_code`) and (`c3`.`dictionary_relation` = 'MARKET_TYPE') and (`c3`.`source_code` = 'CSCS') and (`c3`.`delete_flag` = 0))))
WHERE (`a`.`delete_flag` = 0)) `bond_basicinfo`"

=== 来源表分组分析 ===

视图1,视图2,视图3,来源表
dim_company_basic,dim_company_basic_detail,dwd_company_basic,cs_info_dw.dwd_company_basic
bond_basicinfo,bond_basicinfo_dw,,"cs_info_dw.dwd_bond_basic_info, cs_info_dw.dim_dictionary_relation, cs_info_dw.dwd_lkp_char_code"
bond_pledge,bond_pledge_cm,,cs_info_dw.dwd_bond_pledge
cfg_dict_mapping_rela,dict_abs_mapping_rela,,cs_info_dw.dim_dictionary_relation
company_basicinfo,compy_basicinfo,,"cs_info_dw.dwd_company_basic, cs_info_dw.dim_dictionary_info"
company_core,compy_core,,"cs_info_dw.dwd_company_core, cs_info_dw.dwd_company_basic"
company_shareholders_opt,compy_shareholders_optimized,,cs_info_dw.dwd_company_shareholders_opt
dim_security,dwd_security,,cs_info_dw.dwd_security
bond_absinfo,,,cs_info_dw.dwd_bond_abs_info
bond_actcashflow,,,cs_info_dw.dwd_bond_actcashflow
bond_analysiscsi,,,cs_info_dw.dwd_bond_valuation_csi
bond_cbinfo,,,cs_info_dw.dwd_bond_convertible_bond
bond_cbuyredem,,,cs_info_dw.dwd_bond_convertible_redeem
bond_chnbdyc,,,cs_info_dw.dwd_bond_chnbdyc
bond_creditchg,,,"cs_info_dw.dwd_bond_credit_change, cs_info_dw.dwd_company_basic"
bond_expectcash,,,cs_info_dw.dwd_bond_expect_cash
bond_imprating_xw,,,"cs_info_dw.dwd_bond_imp_rating_xw, cs_info_dw.dwd_security"
bond_issue,,,cs_info_dw.dwd_bond_issue
bond_ncconvrate,,,cs_info_dw.dwd_bond_convert_rate
bond_opbuyredem,,,cs_info_dw.dwd_bond_option_buy_redeem
bond_opvolchg,,,"cs_info_dw.dwd_bond_vol_change, cs_info_dw.dwd_bond_basic_info"
bond_party,,,cs_info_dw.dwd_bond_party
bond_payment_plus,,,cs_info_dw.dwd_bond_payment_plus
bond_rating_yy_record,,,cs_info_dw.dwd_bond_rating_yy_record
bond_redempprin,,,cs_info_dw.dwd_bond_redeem_principal
bond_register,,,cs_info_dw.dwd_bond_register
bond_terms,,,cs_info_dw.dwd_bond_terms
bond_tradt,,,"cs_info_dw.dwd_bond_trade_date, cs_info_dw.dwd_lkp_char_code, cs_info_dw.dim_dictionary_relation"
bond_valuations,,,cs_info_dw.dwd_bond_valuations
bond_violation,,,"cs_info_dw.dwd_bond_violation, cs_info_dw.dwd_security"
bond_warrantor,,,cs_info_dw.dwd_bond_warrantor
bond_warrantor_cm,,,"cs_info_dw.dwd_bond_warrantor, cs_info_dw.dwd_company_basic"
capital_top_ari_new,,,cs_info_dw.dwd_capital_top_ari_new
cm_bond_basicinfo,,,"cs_info_dw.dwd_bond_basic_info, cs_info_dw.dwd_security, cs_info_dw.dwd_lkp_char_code, cs_info_dw.dim_dictionary_relation"
cm_security,,,"cs_info_dw.dwd_security, cs_info_dw.dwd_company_basic, cs_info_dw.dwd_company_basic_extend, cs_platform.manual_product, cs_platform.manual_product_party"
company_affilparty,,,"cs_info_dw.dwd_company_affiliate_party, cs_info_dw.dim_dictionary_relation"
company_balancesheet,,,cs_info_dw.dwd_factor_balance_sheet
company_bankaddfin,,,cs_info_dw.dwd_company_bank_add_fin
company_bondissuer,,,cs_info_dw.dwd_company_bond_issuer
company_cashflow,,,cs_info_dw.dwd_factor_cash_flow
company_category_xygs,,,"cs_info_dw.dwd_company_category, cs_info_dw.dwd_company_basic"
company_chgnm,,,cs_info_dw.dwd_company_change_name
company_company_industry,,,"cs_info_dw.dwd_company_industry, cs_info_dw.dim_dictionary_relation"
company_creditrating,,,cs_info_dw.dwd_company_credit_rating
company_creditrating_info,,,"cs_info_dw.dwd_company_credit_rating_info, cs_info_dw.dwd_company_basic"
company_finanaudit,,,cs_info_dw.dwd_company_finance_audit
company_idchg_map,,,cs_info_dw.dwd_company_id_change
company_incomestate,,,cs_info_dw.dwd_factor_income_state
company_insurerindex,,,"cs_info_dw.dwd_company_insurer_index, cs_info_dw.dwd_company_basic"
company_investment_xygs,,,"cs_info_dw.dwd_company_investment, cs_info_dw.dwd_company_basic"
company_name_map,,,"cs_info_dw.dwd_company_name, cs_info_dw.dwd_company_basic"
company_region,,,cs_info_dw.dwd_company_region
company_relationship_xw,,,"cs_info_dw.dwd_company_relationship, cs_info_dw.dwd_company_basic"
company_reve_industry,,,cs_info_dw.dwd_company_revenue_industry
company_reve_prod,,,cs_info_dw.dwd_company_revenue_product
company_reve_region,,,cs_info_dw.dwd_company_revenue_region
company_secriskindex,,,cs_info_dw.dwd_company_sec_risk_index
company_security_xw,,,cs_info_dw.dwd_security_company_xw
company_shareholder,,,cs_info_dw.dwd_company_shareholder
compy_executivesinfo,,,cs_info_dw.dwd_company_executives
compy_fcoloan,,,cs_info_dw.dwd_company_fco_loan
compy_yy_ctextinfo,,,cs_info_dw.dwd_compy_yy_ctextinfo
compy_yy_cyextinfo,,,cs_info_dw.dwd_compy_yy_cyextinfo
compy_yy_jrextinfo,,,cs_info_dw.dwd_compy_yy_jrextinfo
court_announce_basicinfo_jx,,,cs_info_dw.dwd_law_court_anne_basic
credit_blacklist_basicinfo_jx,,,cs_info_dw.dwd_credit_blacklist_basic
credit_honesty_basicinfo_jx,,,cs_info_dw.dwd_credit_honesty_basic
credit_violation_basicinfo_jx,,,cs_info_dw.dwd_credit_violation_basic
creditrisk_bond_party,,,"cs_info_dw.dwd_bond_party, cs_info_dw.dwd_company_basic, cs_platform.cm_manual_bond_party"
creditrisk_bond_rating_rec,,,cs_platform.bond_rating_record
creditrisk_bond_warrantor,,,"cs_info_dw.dwd_bond_warrantor, cs_info_dw.dwd_bond_party, cs_platform.ind_maincode"
creditrisk_quota_subj_cpy,,,"cs_platform.creditrisk_quota_limit_kind, cs_platform.creditrisk_quota_limit_set, cs_platform.creditrisk_quota_tactics_info, cs_platform.ind_res_report"
creditrisk_quota_subj_psn,,,"cs_platform.bond_tradt, cs_platform.nes_product_balance_his, cs_platform.nes_system_quota, cs_platform.uni_customer_mapping"
creditrisk_rating_cpexposure,,,"cs_info_dw.dwd_company_exposure_xw, cs_info_dw.dwd_company_basic, cs_platform.cm_manual_company_exposure, cs_platform.ind_maincode"
creditrisk_rating_display,,,cs_platform.rating_display
creditrisk_rating_record,,,"cs_platform.rating_record, cs_platform.compy_basicinfo"
dict_abs_recursion,,,"cs_info_dw.dim_dictionary_info, cs_platform.abs_common_manual_dict_recur, cs_info_dw.dwd_lkp_charcode_abs"
dict_info_recursion,,,cs_info_dw.dim_dictionary_info
dim_company_basic_extend,,,cs_info_dw.dwd_company_basic_extend
dwd_company_core,,,cs_info_dw.dwd_company_core
dwd_company_core_relation,,,cs_info_dw.dwd_company_core_relation
dwd_security_financial_product,,,cs_info_dw.dwd_security_financial_product
economy_cpi,,,cs_info_dw.dws_economy_cpi
economy_dl_benchmark,,,cs_info_dw.dws_economy_dl_benchmark
economy_finance_scale,,,cs_info_dw.dws_economy_finance_scale
economy_gdp,,,cs_info_dw.dws_economy_gdp
economy_gold,,,cs_info_dw.dws_economy_gold
economy_hp,,,cs_info_dw.dws_economy_hp
economy_income,,,cs_info_dw.dws_economy_income
economy_invest,,,cs_info_dw.dws_economy_invest
economy_loan,,,cs_info_dw.dws_economy_loan
