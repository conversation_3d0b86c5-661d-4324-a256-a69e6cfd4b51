视图合并结果
基于merged_views_*.json文件生成的视图合并分析结果

created_time,optimization_notes,合成表,合成表DDL,来源视图1,来源视图1DDL,来源视图2,来源视图2DDL,来源视图3,来源视图3DDL,来源视图4,来源视图4DDL,来源视图5,来源视图5DDL
2025-08-04 18:30:30,统一了字段命名和表达式，消除了冗余; 直接从源表查询，避免了视图嵌套; 简化了WHERE条件，保持了数据完整性; 使用API生成; 合并策略: direct; 复杂度评估: 0.60,bond_pledge_bond_pledge_cm_merged,"CREATE OR REPLACE VIEW bond_pledge_bond_pledge_cm_merged AS
SELECT
    t.pk_id AS BOND_PLEDGE_SID,
    t.security_global_id AS SECURITY_GLOBAL_ID,
    t.security_global_id AS SECINNER_ID,
    t.notice_date AS NOTICE_DT,
    t.pledge_name AS PLEDGE_NM,
    t.pledge_type_id AS PLEDGE_TYPE_ID,
    t.pledge_desc AS PLEDGE_DESC,
    t.pledge_owner_code AS PLEDGE_OWNER_CODE,
    t.pledge_owner AS PLEDGE_OWNER,
    t.pledge_value AS PLEDGE_VALUE,
    t.priority_value AS PRIORITY_VALUE,
    t.pledge_depend_id AS PLEDGE_DEPEND_ID,
    t.pledge_control_id AS PLEDGE_CONTROL_ID,
    t.region AS REGION,
    t.mitigation_value AS MITIGATION_VALUE,
    t.delete_flag AS SRC_ISDEL,
    IFNULL(t.SOURCE_CODE,'CSCS') AS SRC_CD,
    t.delete_flag AS IS_DEL,
    t.create_dt AS CREATE_DT,
    t.updt_dt AS UPDT_DT
FROM
    cs_info_dw.dwd_bond_pledge t
WHERE
    (t.SOURCE_CODE = 'MANUAL' OR t.SOURCE_CODE IS NULL OR t.SOURCE_CODE != 'MANUAL')",bond_pledge,"CREATE OR REPLACE VIEW bond_pledge AS
SELECT
    `s`.`BOND_PLEDGE_SID` AS `BOND_PLEDGE_SID`,
    `s`.`security_global_id` AS `security_global_id`,
    `s`.`SECINNER_ID` AS `SECINNER_ID`,
    `s`.`NOTICE_DT` AS `NOTICE_DT`,
    `s`.`PLEDGE_NM` AS `PLEDGE_NM`,
    `s`.`PLEDGE_TYPE_ID` AS `PLEDGE_TYPE_ID`,
    `s`.`PLEDGE_DESC` AS `PLEDGE_DESC`,
    `s`.`PLEDGE_OWNER_CODE` AS `PLEDGE_OWNER_CODE`,
    `s`.`PLEDGE_OWNER` AS `PLEDGE_OWNER`,
    `s`.`PLEDGE_VALUE` AS `PLEDGE_VALUE`,
    `s`.`PRIORITY_VALUE` AS `PRIORITY_VALUE`,
    `s`.`PLEDGE_DEPEND_ID` AS `PLEDGE_DEPEND_ID`,
    `s`.`PLEDGE_CONTROL_ID` AS `PLEDGE_CONTROL_ID`,
    `s`.`REGION` AS `REGION`,
    `s`.`MITIGATION_VALUE` AS `MITIGATION_VALUE`,
    `s`.`SRC_ISDEL` AS `SRC_ISDEL`,
    `s`.`SRC_CD` AS `SRC_CD`,
    `s`.`IS_DEL` AS `IS_DEL`,
    `s`.`CREATE_DT` AS `CREATE_DT`,
    `s`.`UPDT_DT` AS `UPDT_DT`
FROM (select `d`.`BOND_PLEDGE_SID` AS `BOND_PLEDGE_SID`,`d`.`security_global_id` AS `security_global_id`,`d`.`SECINNER_ID` AS `SECINNER_ID`,`d`.`NOTICE_DT` AS `NOTICE_DT`,`d`.`PLEDGE_NM` AS `PLEDGE_NM`,`d`.`PLEDGE_TYPE_ID` AS `PLEDGE_TYPE_ID`,`d`.`PLEDGE_DESC` AS `PLEDGE_DESC`,`d`.`PLEDGE_OWNER_CODE` AS `PLEDGE_OWNER_CODE`,`d`.`PLEDGE_OWNER` AS `PLEDGE_OWNER`,`d`.`PLEDGE_VALUE` AS `PLEDGE_VALUE`,`d`.`PRIORITY_VALUE` AS `PRIORITY_VALUE`,`d`.`PLEDGE_DEPEND_ID` AS `PLEDGE_DEPEND_ID`,`d`.`PLEDGE_CONTROL_ID` AS `PLEDGE_CONTROL_ID`,`d`.`REGION` AS `REGION`,`d`.`MITIGATION_VALUE` AS `MITIGATION_VALUE`,`d`.`SRC_ISDEL` AS `SRC_ISDEL`,`d`.`SRC_CD` AS `SRC_CD`,`d`.`IS_DEL` AS `IS_DEL`,`d`.`CREATE_DT` AS `CREATE_DT`,`d`.`UPDT_DT` AS `UPDT_DT`,row_number() OVER (PARTITION BY `d`.`security_global_id`,`d`.`PLEDGE_NM`
ORDER BY `d`.`UPDT_DT` desc,`d`.`CREATE_DT` desc )  AS `RN`
FROM (select `t`.`pk_id` AS `BOND_PLEDGE_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`pledge_name` AS `PLEDGE_NM`,`t`.`pledge_type_id` AS `PLEDGE_TYPE_ID`,`t`.`pledge_desc` AS `PLEDGE_DESC`,`t`.`pledge_owner_code` AS `PLEDGE_OWNER_CODE`,`t`.`pledge_owner` AS `PLEDGE_OWNER`,`t`.`pledge_value` AS `PLEDGE_VALUE`,`t`.`priority_value` AS `PRIORITY_VALUE`,`t`.`pledge_depend_id` AS `PLEDGE_DEPEND_ID`,`t`.`pledge_control_id` AS `PLEDGE_CONTROL_ID`,`t`.`region` AS `REGION`,`t`.`mitigation_value` AS `MITIGATION_VALUE`,`t`.`delete_flag` AS `SRC_ISDEL`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`
FROM `cs_info_dw`.`dwd_bond_pledge` `t`) `d`) `s`
WHERE (`s`.`RN` = 1)",bond_pledge_cm,"CREATE OR REPLACE VIEW bond_pledge_cm AS
SELECT
    `bond_pledge`.`bond_pledge_sid` AS `bond_pledge_sid`,
    `bond_pledge`.`security_global_id` AS `security_global_id`,
    `bond_pledge`.`secinner_id` AS `secinner_id`,
    `bond_pledge`.`notice_dt` AS `notice_dt`,
    `bond_pledge`.`pledge_nm` AS `pledge_nm`,
    `bond_pledge`.`pledge_type_id` AS `pledge_type_id`,
    `bond_pledge`.`pledge_desc` AS `pledge_desc`,
    `bond_pledge`.`pledge_owner_code` AS `pledge_owner_code`,
    `bond_pledge`.`pledge_owner` AS `pledge_owner`,
    `bond_pledge`.`pledge_value` AS `pledge_value`,
    `bond_pledge`.`priority_value` AS `priority_value`,
    `bond_pledge`.`pledge_depend_id` AS `pledge_depend_id`,
    `bond_pledge`.`pledge_control_id` AS `pledge_control_id`,
    `bond_pledge`.`region` AS `region`,
    `bond_pledge`.`mitigation_value` AS `mitigation_value`,
    `bond_pledge`.`src_isdel` AS `src_isdel`,
    `bond_pledge`.`SRC_CD` AS `src_cd`,
    `bond_pledge`.`IS_DEL` AS `is_del`,
    `bond_pledge`.`CREATE_DT` AS `create_dt`,
    `bond_pledge`.`UPDT_DT` AS `updt_dt`
FROM (select `t`.`pk_id` AS `bond_pledge_sid`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `secinner_id`,`t`.`notice_date` AS `notice_dt`,`t`.`pledge_name` AS `pledge_nm`,`t`.`pledge_type_id` AS `pledge_type_id`,`t`.`pledge_desc` AS `pledge_desc`,`t`.`pledge_owner_code` AS `pledge_owner_code`,`t`.`pledge_owner` AS `pledge_owner`,`t`.`pledge_value` AS `pledge_value`,`t`.`priority_value` AS `priority_value`,`t`.`pledge_depend_id` AS `pledge_depend_id`,`t`.`pledge_control_id` AS `pledge_control_id`,`t`.`region` AS `region`,`t`.`mitigation_value` AS `mitigation_value`,`t`.`delete_flag` AS `src_isdel`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`
FROM `cs_info_dw`.`dwd_bond_pledge` `t`
WHERE (`t`.`SOURCE_CODE` = 'MANUAL')) `bond_pledge`",,,,,,
