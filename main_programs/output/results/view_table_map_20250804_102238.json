{"view_table_map": {"bond_absinfo": ["cs_info_dw.dwd_bond_abs_info"], "bond_actcashflow": ["cs_info_dw.dwd_bond_actcashflow"], "bond_analysiscsi": ["cs_info_dw.dwd_bond_valuation_csi"], "bond_basicinfo": ["cs_info_dw.dwd_bond_basic_info", "cs_info_dw.dim_dictionary_relation", "cs_info_dw.dwd_lkp_char_code"], "bond_basicinfo_dw": ["cs_info_dw.dwd_bond_basic_info", "cs_info_dw.dim_dictionary_relation", "cs_info_dw.dwd_lkp_char_code"], "bond_cbinfo": ["cs_info_dw.dwd_bond_convertible_bond"], "bond_cbuyredem": ["cs_info_dw.dwd_bond_convertible_redeem"], "bond_chnbdyc": ["cs_info_dw.dwd_bond_chnbdyc"], "bond_creditchg": ["cs_info_dw.dwd_bond_credit_change", "cs_info_dw.dwd_company_basic"], "bond_expectcash": ["cs_info_dw.dwd_bond_expect_cash"], "bond_imprating_xw": ["cs_info_dw.dwd_bond_imp_rating_xw", "cs_info_dw.dwd_security"], "bond_issue": ["cs_info_dw.dwd_bond_issue"], "bond_ncconvrate": ["cs_info_dw.dwd_bond_convert_rate"], "bond_opbuyredem": ["cs_info_dw.dwd_bond_option_buy_redeem"], "bond_opvolchg": ["cs_info_dw.dwd_bond_vol_change", "cs_info_dw.dwd_bond_basic_info"], "bond_party": ["cs_info_dw.dwd_bond_party"], "bond_payment_plus": ["cs_info_dw.dwd_bond_payment_plus"], "bond_pledge": ["cs_info_dw.dwd_bond_pledge"], "bond_pledge_cm": ["cs_info_dw.dwd_bond_pledge"], "bond_rating_yy_record": ["cs_info_dw.dwd_bond_rating_yy_record"], "bond_redempprin": ["cs_info_dw.dwd_bond_redeem_principal"], "bond_register": ["cs_info_dw.dwd_bond_register"], "bond_terms": ["cs_info_dw.dwd_bond_terms"], "bond_tradt": ["cs_info_dw.dwd_bond_trade_date", "cs_info_dw.dwd_lkp_char_code", "cs_info_dw.dim_dictionary_relation"], "bond_valuations": ["cs_info_dw.dwd_bond_valuations"], "bond_violation": ["cs_info_dw.dwd_bond_violation", "cs_info_dw.dwd_security"], "bond_warrantor": ["cs_info_dw.dwd_bond_warrantor"], "bond_warrantor_cm": ["cs_info_dw.dwd_bond_warrantor", "cs_info_dw.dwd_company_basic"], "capital_top_ari_new": ["cs_info_dw.dwd_capital_top_ari_new"], "cfg_dict_mapping_rela": ["cs_info_dw.dim_dictionary_relation"], "cm_bond_basicinfo": ["cs_info_dw.dwd_bond_basic_info", "cs_info_dw.dwd_security", "cs_info_dw.dwd_lkp_char_code", "cs_info_dw.dim_dictionary_relation"], "cm_security": ["cs_info_dw.dwd_security", "cs_info_dw.dwd_company_basic", "cs_info_dw.dwd_company_basic_extend", "cs_platform.manual_product", "cs_platform.manual_product_party"], "company_affilparty": ["cs_info_dw.dwd_company_affiliate_party", "cs_info_dw.dim_dictionary_relation"], "company_balancesheet": ["cs_info_dw.dwd_factor_balance_sheet"], "company_bankaddfin": ["cs_info_dw.dwd_company_bank_add_fin"], "company_basicinfo": ["cs_info_dw.dwd_company_basic", "cs_info_dw.dim_dictionary_info"], "company_bondissuer": ["cs_info_dw.dwd_company_bond_issuer"], "company_cashflow": ["cs_info_dw.dwd_factor_cash_flow"], "company_category_xygs": ["cs_info_dw.dwd_company_category", "cs_info_dw.dwd_company_basic"], "company_chgnm": ["cs_info_dw.dwd_company_change_name"], "company_company_industry": ["cs_info_dw.dwd_company_industry", "cs_info_dw.dim_dictionary_relation"], "company_core": ["cs_info_dw.dwd_company_core", "cs_info_dw.dwd_company_basic"], "company_creditrating": ["cs_info_dw.dwd_company_credit_rating"], "company_creditrating_info": ["cs_info_dw.dwd_company_credit_rating_info", "cs_info_dw.dwd_company_basic"], "company_finanaudit": ["cs_info_dw.dwd_company_finance_audit"], "company_idchg_map": ["cs_info_dw.dwd_company_id_change"], "company_incomestate": ["cs_info_dw.dwd_factor_income_state"], "company_insurerindex": ["cs_info_dw.dwd_company_insurer_index", "cs_info_dw.dwd_company_basic"], "company_investment_xygs": ["cs_info_dw.dwd_company_investment", "cs_info_dw.dwd_company_basic"], "company_name_map": ["cs_info_dw.dwd_company_name", "cs_info_dw.dwd_company_basic"]}, "view_details": [{"view_name": "bond_absinfo", "view_definition": "select `t`.`pk_id` AS `BOND_ABS_INFO_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`abs_code` AS `ABS_CD`,`t`.`abs_short_name` AS `ABS_SNM`,`t`.`abs_name` AS `ABS_NM`,`t`.`found_date` AS `FOUND_DT`,`t`.`base_asset_id` AS `BASE_ASSET_ID`,`t`.`base_asset2_id` AS `BASE_ASSET2_ID`,`t`.`base_asset_desc` AS `BASE_ASSET_DESC`,`t`.`priority_level_id` AS `PRIORITY_LEVEL_ID`,`t`.`credit_type_in_id` AS `CREDIT_TYPEIN_ID`,`t`.`remark` AS `REMARK`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_PORTFOLIO_CD`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,1 AS `CREATE_BY`,`t`.`create_dt` AS `CREATE_DT`,1 AS `UPDT_BY`,`t`.`updt_dt` AS `UPDT_DT`,1 AS `VERSION`,0 AS `STATUS` from `cs_info_dw`.`dwd_bond_abs_info` `t`", "source_tables": ["cs_info_dw.dwd_bond_abs_info"], "table_count": 1, "created_time": "2025-08-04T10:20:41.457255"}, {"view_name": "bond_actcashflow", "view_definition": "select `t`.`pk_id` AS `bond_actcashflow_id`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `secinner_id`,`t`.`notice_dt` AS `notice_dt`,`t`.`bond_combine_cd` AS `bond_combine_cd`,`t`.`security_cd` AS `security_cd`,`t`.`start_dt` AS `start_dt`,`t`.`end_dt` AS `end_dt`,`t`.`coupon_interest_rate` AS `coupon_interest_rate`,`t`.`record_dt` AS `record_dt`,date_format(`t`.`plan_payment_dt`,'%Y%m%d') AS `plan_payment_dt`,`t`.`act_payment_dt` AS `act_payment_dt`,`t`.`interest_payable_pcr` AS `interest_payable_pcr`,`t`.`capital_payable_pcr` AS `capital_payable_pcr`,`t`.`cashflow_total_pcr` AS `cashflow_total_pcr`,`t`.`residual_capital_pcr` AS `residual_capital_pcr`,`t`.`honour_net_price` AS `honour_net_price`,`t`.`is_valid` AS `is_valid`,`t`.`is_newest` AS `is_newest`,`t`.`ex_dividend_dt` AS `ex_dividend_dt`,`t`.`honour_object` AS `honour_object`,`t`.`pi_end_dt` AS `pi_end_dt`,`t`.`security_cd` AS `src_secinner_cd`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_actcashflow` `t`", "source_tables": ["cs_info_dw.dwd_bond_actcashflow"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_analysiscsi", "view_definition": "select `t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`valuation_dt` AS `TRADE_DT`,NULL AS `B_INFO_TYPE`,`t`.`trd_market_id` AS `B_INFO_LISTINGMARKETNUMBERS`,`t`.`mrty_full_price` AS `B_ANAL_DIRTY_CSI`,`t`.`mrty_ytm` AS `B_ANAL_YIELD_CSI`,`t`.`mrty_mduration` AS `B_ANAL_MODIDURA_CSI`,`t`.`mrty_convexity` AS `B_ANAL_CNVXTY_CSI`,`t`.`mrty_netprice` AS `B_ANAL_NET_CSI`,`t`.`ai` AS `B_ANAL_ACCRINT_CSI`,`t`.`delete_flag` AS `IS_DEL`,`t`.`updt_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_valuation_csi` `t`", "source_tables": ["cs_info_dw.dwd_bond_valuation_csi"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_basicinfo", "view_definition": "select `bond_basicinfo`.`security_global_id` AS `security_global_id`,`bond_basicinfo`.`secinner_id` AS `secinner_id`,`bond_basicinfo`.`security_cscs_code` AS `security_cscs_code`,`bond_basicinfo`.`company_global_id` AS `company_global_id`,`bond_basicinfo`.`company_id` AS `company_id`,`bond_basicinfo`.`security_cd` AS `security_cd`,`bond_basicinfo`.`security_nm` AS `security_nm`,`bond_basicinfo`.`security_snm` AS `security_snm`,`bond_basicinfo`.`spell` AS `spell`,`bond_basicinfo`.`security_type_id` AS `security_type_id`,`bond_basicinfo`.`issue_year` AS `issue_year`,`bond_basicinfo`.`issue_num` AS `issue_num`,`bond_basicinfo`.`currency` AS `currency`,`bond_basicinfo`.`trade_market_id` AS `trade_market_id`,`bond_basicinfo`.`notice_dt` AS `notice_dt`,`bond_basicinfo`.`src_create_dt` AS `src_create_dt`,`bond_basicinfo`.`public_dt` AS `public_dt`,`bond_basicinfo`.`public_announce_dt` AS `public_announce_dt`,`bond_basicinfo`.`issue_dt` AS `issue_dt`,`bond_basicinfo`.`frst_value_dt` AS `frst_value_dt`,`bond_basicinfo`.`last_value_dt` AS `last_value_dt`,`bond_basicinfo`.`puttable_dt` AS `puttable_dt`,`bond_basicinfo`.`mrty_dt` AS `mrty_dt`,`bond_basicinfo`.`payment_dt` AS `payment_dt`,`bond_basicinfo`.`redem_dt` AS `redem_dt`,`bond_basicinfo`.`delist_dt` AS `delist_dt`,`bond_basicinfo`.`pay_day` AS `pay_day`,`bond_basicinfo`.`bond_type1_id` AS `bond_type1_id`,`bond_basicinfo`.`bond_type2_id` AS `bond_type2_id`,`bond_basicinfo`.`bond_form_id` AS `bond_form_id`,`bond_basicinfo`.`other_nature` AS `other_nature`,`bond_basicinfo`.`credit_rating` AS `credit_rating`,`bond_basicinfo`.`issue_vol` AS `issue_vol`,`bond_basicinfo`.`listvolsz` AS `listvolsz`,`bond_basicinfo`.`listvolsh` AS `listvolsh`,`bond_basicinfo`.`bond_period` AS `bond_period`,`bond_basicinfo`.`par_value` AS `par_value`,`bond_basicinfo`.`issue_price` AS `issue_price`,`bond_basicinfo`.`coupon_type_cd` AS `coupon_type_cd`,`bond_basicinfo`.`pay_type_cd` AS `pay_type_cd`,`bond_basicinfo`.`pay_desc` AS `pay_desc`,`bond_basicinfo`.`coupon_rule_cd` AS `coupon_rule_cd`,`bond_basicinfo`.`payment_type_cd` AS `payment_type_cd`,`bond_basicinfo`.`coupon_rate` AS `coupon_rate`,`bond_basicinfo`.`floor_rate` AS `floor_rate`,`bond_basicinfo`.`bnchmk_spread` AS `bnchmk_spread`,`bond_basicinfo`.`bnchmk_id` AS `bnchmk_id`,`bond_basicinfo`.`rate_desc` AS `rate_desc`,`bond_basicinfo`.`pay_peryear` AS `pay_peryear`,`bond_basicinfo`.`add_rate` AS `add_rate`,`bond_basicinfo`.`puttable_price` AS `puttable_price`,`bond_basicinfo`.`expect_rate` AS `expect_rate`,`bond_basicinfo`.`issue_type_cd` AS `issue_type_cd`,`bond_basicinfo`.`refe_rate` AS `refe_rate`,`bond_basicinfo`.`add_issue_num` AS `add_issue_num`,`bond_basicinfo`.`is_cross` AS `is_cross`,`bond_basicinfo`.`is_floor_rate` AS `is_floor_rate`,`bond_basicinfo`.`is_adjust_type` AS `is_adjust_type`,`bond_basicinfo`.`is_redem` AS `is_redem`,`bond_basicinfo`.`is_plit_debt` AS `is_plit_debt`,`bond_basicinfo`.`is_puttable` AS `is_puttable`,`bond_basicinfo`.`is_change` AS `is_change`,`bond_basicinfo`.`fwd_rate` AS `fwd_rate`,`bond_basicinfo`.`redem_price` AS `redem_price`,`bond_basicinfo`.`swaps_cd` AS `swaps_cd`,`bond_basicinfo`.`tax_rate` AS `tax_rate`,`bond_basicinfo`.`coupon_style_cd` AS `coupon_style_cd`,`bond_basicinfo`.`Option_Termdes` AS `option_termdes`,`bond_basicinfo`.`Base_Asset` AS `base_asset`,`bond_basicinfo`.`coupon_method_cd` AS `coupon_method_cd`,`bond_basicinfo`.`bond_option` AS `bond_option`,`bond_basicinfo`.`credit_typein_id` AS `credit_typein_id`,`bond_basicinfo`.`credit_typeout_id` AS `credit_typeout_id`,`bond_basicinfo`.`remark` AS `remark`,`bond_basicinfo`.`src_portfolio_cd` AS `src_portfolio_cd`,`bond_basicinfo`.`src_isdel` AS `src_isdel`,`bond_basicinfo`.`SRC_CD` AS `src_cd`,`bond_basicinfo`.`IS_DEL` AS `is_del`,`bond_basicinfo`.`CREATE_DT` AS `create_dt`,`bond_basicinfo`.`UPDT_DT` AS `updt_dt`,`bond_basicinfo`.`is_abs` AS `is_abs`,`bond_basicinfo`.`is_sub` AS `is_sub`,`bond_basicinfo`.`is_continuous` AS `is_continuous` from (select `a`.`security_global_id` AS `security_global_id`,`a`.`security_global_id` AS `secinner_id`,`a`.`security_cscs_code` AS `security_cscs_code`,`a`.`company_global_id` AS `company_global_id`,`a`.`company_global_id` AS `company_id`,`a`.`security_code` AS `security_cd`,`a`.`security_name` AS `security_nm`,`a`.`security_short_name` AS `security_snm`,`a`.`spell` AS `spell`,ifnull(`mp`.`dictionary_relation_code`,`a`.`security_type_id`) AS `security_type_id`,`a`.`issue_year` AS `issue_year`,`a`.`issue_num` AS `issue_num`,ifnull(`c2`.`dictionary_relation_code`,`a`.`currency`) AS `currency`,ifnull(`c3`.`dictionary_relation_code`,`a`.`trade_market_id`) AS `trade_market_id`,`a`.`notice_date` AS `notice_dt`,`a`.`create_date` AS `src_create_dt`,`a`.`public_date` AS `public_dt`,`a`.`public_announce_date` AS `public_announce_dt`,`a`.`issue_date` AS `issue_dt`,ifnull(`a`.`first_value_date`,`a`.`issue_date`) AS `frst_value_dt`,`a`.`last_value_date` AS `last_value_dt`,`a`.`put_table_date` AS `puttable_dt`,`a`.`maturity_date` AS `mrty_dt`,`a`.`payment_date` AS `payment_dt`,`a`.`redeem_date` AS `redem_dt`,`a`.`delist_date` AS `delist_dt`,`a`.`pay_day` AS `pay_day`,`mp`.`dictionary_relation_code` AS `bond_type1_id`,`mp`.`dictionary_relation_code` AS `bond_type2_id`,`a`.`bond_form_id` AS `bond_form_id`,`a`.`other_nature` AS `other_nature`,`a`.`credit_rating` AS `credit_rating`,`a`.`issue_vol` AS `issue_vol`,`a`.`list_vol_sz` AS `listvolsz`,`a`.`list_vol_sh` AS `listvolsh`,`a`.`bond_period` AS `bond_period`,`a`.`par_value` AS `par_value`,`a`.`issue_price` AS `issue_price`,`a`.`coupon_type_code` AS `coupon_type_cd`,`a`.`pay_type_code` AS `pay_type_cd`,`a`.`pay_desc` AS `pay_desc`,`a`.`coupon_rule_code` AS `coupon_rule_cd`,`a`.`payment_type_code` AS `payment_type_cd`,`a`.`coupon_rate` AS `coupon_rate`,`a`.`floor_rate` AS `floor_rate`,`a`.`bnchmk_spread` AS `bnchmk_spread`,`a`.`bnchmk_id` AS `bnchmk_id`,`a`.`rate_desc` AS `rate_desc`,`a`.`pay_per_year` AS `pay_peryear`,`a`.`add_rate` AS `add_rate`,`a`.`put_table_price` AS `puttable_price`,`a`.`expect_rate` AS `expect_rate`,`a`.`issue_type_code` AS `issue_type_cd`,`a`.`reference_rate` AS `refe_rate`,`a`.`add_issue_num` AS `add_issue_num`,`a`.`is_cross` AS `is_cross`,`a`.`is_floor_rate` AS `is_floor_rate`,`a`.`is_adjust_type` AS `is_adjust_type`,`a`.`is_redeem` AS `is_redem`,`a`.`is_plit_debt` AS `is_plit_debt`,`a`.`is_put_table` AS `is_puttable`,`a`.`is_change` AS `is_change`,`a`.`fwd_rate` AS `fwd_rate`,`a`.`redeem_price` AS `redem_price`,`a`.`swaps_code` AS `swaps_cd`,`a`.`tax_rate` AS `tax_rate`,`a`.`coupon_style_code` AS `coupon_style_cd`,`a`.`option_termdes` AS `Option_Termdes`,`a`.`base_asset` AS `Base_Asset`,`a`.`coupon_method_code` AS `coupon_method_cd`,`a`.`bond_option` AS `bond_option`,`a`.`credit_type_in_id` AS `credit_typein_id`,`a`.`credit_type_out_id` AS `credit_typeout_id`,`a`.`remark` AS `remark`,`a`.`src_portfolio_cd` AS `src_portfolio_cd`,`a`.`delete_flag` AS `src_isdel`,ifnull(`a`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`a`.`delete_flag` AS `IS_DEL`,`a`.`create_dt` AS `CREATE_DT`,`a`.`updt_dt` AS `UPDT_DT`,(case when (ifnull(`mp`.`mapping_relation_code`,`mq`.`mapping_relation_code`) like '060007%') then 1 else 0 end) AS `is_abs`,`a`.`is_sub` AS `is_sub`,(case when (`a`.`option_termdes` like '%+N') then 1 else 0 end) AS `is_continuous` from ((((((`cs_info_dw`.`dwd_bond_basic_info` `a` left join (select `n`.`dictionary_relation_code` AS `dictionary_relation_code`,`n`.`mapping_relation_code` AS `mapping_relation_code`,`n`.`source_code` AS `source_code`,`n`.`delete_flag` AS `delete_flag`,`n`.`dictionary_relation` AS `dictionary_relation` from (select `p`.`dictionary_relation_code` AS `dictionary_relation_code`,`p`.`mapping_relation_code` AS `mapping_relation_code`,`p`.`source_code` AS `source_code`,`p`.`delete_flag` AS `delete_flag`,`p`.`dictionary_relation` AS `dictionary_relation`,row_number() OVER (PARTITION BY `p`.`dictionary_relation_code` ORDER BY `p`.`dictionary_relation_id` )  AS `RN` from `cs_info_dw`.`dim_dictionary_relation` `p` where ((`p`.`dictionary_relation` = 'SECURITY_TYPE') and (`p`.`source_code` = 'CSCS'))) `n` where (`n`.`RN` = 1)) `mq` on(((`mq`.`dictionary_relation` = 'SECURITY_TYPE') and (`a`.`security_type_id` = `mq`.`dictionary_relation_code`) and (`mq`.`source_code` = 'CSCS') and (`mq`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `dlcc` on(((`a`.`security_type_id` = cast(`dlcc`.`constant_id` as char charset utf8mb4)) and (`dlcc`.`constant_type` = 201) and (`dlcc`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `mp` on(((`mp`.`dictionary_relation` = 'SECURITY_TYPE') and (`dlcc`.`constant_code` = `mp`.`mapping_relation_code`) and (`mp`.`source_code` = 'CSCS') and (`mp`.`delete_flag` = 0) and (((`a`.`issue_type_code` = 1) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010201)) or ((`a`.`issue_type_code` = 2) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010202)) or ((`dlcc`.`constant_code` <> '060005007') and (1 = 1)))))) left join `cs_info_dw`.`dim_dictionary_relation` `c2` on(((`a`.`currency` = `c2`.`mapping_relation_code`) and (`c2`.`dictionary_relation` = 'COMPY_BASICINFO.CURRENCY') and (`c2`.`source_code` = 'CSCS.MDS') and (`c2`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `c` on(((`c`.`constant_id` = `a`.`trade_market_id`) and (`c`.`constant_type` = 206) and (`c`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `c3` on(((`c`.`constant_code` = `c3`.`mapping_relation_code`) and (`c3`.`dictionary_relation` = 'MARKET_TYPE') and (`c3`.`source_code` = 'CSCS') and (`c3`.`delete_flag` = 0)))) where (`a`.`delete_flag` = 0)) `bond_basicinfo`", "source_tables": ["cs_info_dw.dwd_bond_basic_info", "cs_info_dw.dim_dictionary_relation", "cs_info_dw.dwd_lkp_char_code"], "table_count": 3, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_basicinfo_dw", "view_definition": "select `bond_basicinfo`.`security_global_id` AS `security_global_id`,`bond_basicinfo`.`secinner_id` AS `secinner_id`,`bond_basicinfo`.`security_cscs_code` AS `security_cscs_code`,`bond_basicinfo`.`company_global_id` AS `company_global_id`,`bond_basicinfo`.`company_id` AS `company_id`,`bond_basicinfo`.`security_cd` AS `security_cd`,`bond_basicinfo`.`security_nm` AS `security_nm`,`bond_basicinfo`.`security_snm` AS `security_snm`,`bond_basicinfo`.`spell` AS `spell`,`bond_basicinfo`.`security_type_id` AS `security_type_id`,`bond_basicinfo`.`issue_year` AS `issue_year`,`bond_basicinfo`.`issue_num` AS `issue_num`,`bond_basicinfo`.`currency` AS `currency`,`bond_basicinfo`.`trade_market_id` AS `trade_market_id`,`bond_basicinfo`.`notice_dt` AS `notice_dt`,`bond_basicinfo`.`src_create_dt` AS `src_create_dt`,`bond_basicinfo`.`public_dt` AS `public_dt`,`bond_basicinfo`.`public_announce_dt` AS `public_announce_dt`,`bond_basicinfo`.`issue_dt` AS `issue_dt`,`bond_basicinfo`.`frst_value_dt` AS `frst_value_dt`,`bond_basicinfo`.`last_value_dt` AS `last_value_dt`,`bond_basicinfo`.`puttable_dt` AS `puttable_dt`,`bond_basicinfo`.`mrty_dt` AS `mrty_dt`,`bond_basicinfo`.`payment_dt` AS `payment_dt`,`bond_basicinfo`.`redem_dt` AS `redem_dt`,`bond_basicinfo`.`delist_dt` AS `delist_dt`,`bond_basicinfo`.`pay_day` AS `pay_day`,`bond_basicinfo`.`bond_type1_id` AS `bond_type1_id`,`bond_basicinfo`.`bond_type2_id` AS `bond_type2_id`,`bond_basicinfo`.`bond_form_id` AS `bond_form_id`,`bond_basicinfo`.`other_nature` AS `other_nature`,`bond_basicinfo`.`credit_rating` AS `credit_rating`,`bond_basicinfo`.`issue_vol` AS `issue_vol`,`bond_basicinfo`.`listvolsz` AS `listvolsz`,`bond_basicinfo`.`listvolsh` AS `listvolsh`,`bond_basicinfo`.`bond_period` AS `bond_period`,`bond_basicinfo`.`par_value` AS `par_value`,`bond_basicinfo`.`issue_price` AS `issue_price`,`bond_basicinfo`.`coupon_type_cd` AS `coupon_type_cd`,`bond_basicinfo`.`pay_type_cd` AS `pay_type_cd`,`bond_basicinfo`.`pay_desc` AS `pay_desc`,`bond_basicinfo`.`coupon_rule_cd` AS `coupon_rule_cd`,`bond_basicinfo`.`payment_type_cd` AS `payment_type_cd`,`bond_basicinfo`.`coupon_rate` AS `coupon_rate`,`bond_basicinfo`.`floor_rate` AS `floor_rate`,`bond_basicinfo`.`bnchmk_spread` AS `bnchmk_spread`,`bond_basicinfo`.`bnchmk_id` AS `bnchmk_id`,`bond_basicinfo`.`rate_desc` AS `rate_desc`,`bond_basicinfo`.`pay_peryear` AS `pay_peryear`,`bond_basicinfo`.`add_rate` AS `add_rate`,`bond_basicinfo`.`puttable_price` AS `puttable_price`,`bond_basicinfo`.`expect_rate` AS `expect_rate`,`bond_basicinfo`.`issue_type_cd` AS `issue_type_cd`,`bond_basicinfo`.`refe_rate` AS `refe_rate`,`bond_basicinfo`.`add_issue_num` AS `add_issue_num`,`bond_basicinfo`.`is_cross` AS `is_cross`,`bond_basicinfo`.`is_floor_rate` AS `is_floor_rate`,`bond_basicinfo`.`is_adjust_type` AS `is_adjust_type`,`bond_basicinfo`.`is_redem` AS `is_redem`,`bond_basicinfo`.`is_plit_debt` AS `is_plit_debt`,`bond_basicinfo`.`is_puttable` AS `is_puttable`,`bond_basicinfo`.`is_change` AS `is_change`,`bond_basicinfo`.`fwd_rate` AS `fwd_rate`,`bond_basicinfo`.`redem_price` AS `redem_price`,`bond_basicinfo`.`swaps_cd` AS `swaps_cd`,`bond_basicinfo`.`tax_rate` AS `tax_rate`,`bond_basicinfo`.`coupon_style_cd` AS `coupon_style_cd`,`bond_basicinfo`.`Option_Termdes` AS `option_termdes`,`bond_basicinfo`.`Base_Asset` AS `base_asset`,`bond_basicinfo`.`coupon_method_cd` AS `coupon_method_cd`,`bond_basicinfo`.`bond_option` AS `bond_option`,`bond_basicinfo`.`credit_typein_id` AS `credit_typein_id`,`bond_basicinfo`.`credit_typeout_id` AS `credit_typeout_id`,`bond_basicinfo`.`remark` AS `remark`,`bond_basicinfo`.`src_portfolio_cd` AS `src_portfolio_cd`,`bond_basicinfo`.`src_isdel` AS `src_isdel`,`bond_basicinfo`.`SRC_CD` AS `src_cd`,`bond_basicinfo`.`IS_DEL` AS `is_del`,`bond_basicinfo`.`CREATE_DT` AS `create_dt`,`bond_basicinfo`.`UPDT_DT` AS `updt_dt`,`bond_basicinfo`.`is_abs` AS `is_abs`,`bond_basicinfo`.`is_sub` AS `is_sub` from (select `a`.`security_global_id` AS `security_global_id`,`a`.`security_global_id` AS `secinner_id`,`a`.`security_cscs_code` AS `security_cscs_code`,`a`.`company_global_id` AS `company_global_id`,`a`.`company_global_id` AS `company_id`,`a`.`security_code` AS `security_cd`,`a`.`security_name` AS `security_nm`,`a`.`security_short_name` AS `security_snm`,`a`.`spell` AS `spell`,ifnull(`mp`.`dictionary_relation_code`,`a`.`security_type_id`) AS `security_type_id`,`a`.`issue_year` AS `issue_year`,`a`.`issue_num` AS `issue_num`,ifnull(`c2`.`dictionary_relation_code`,`a`.`currency`) AS `currency`,ifnull(`c3`.`dictionary_relation_code`,`a`.`trade_market_id`) AS `trade_market_id`,`a`.`notice_date` AS `notice_dt`,`a`.`create_date` AS `src_create_dt`,`a`.`public_date` AS `public_dt`,`a`.`public_announce_date` AS `public_announce_dt`,`a`.`issue_date` AS `issue_dt`,ifnull(`a`.`first_value_date`,`a`.`issue_date`) AS `frst_value_dt`,`a`.`last_value_date` AS `last_value_dt`,`a`.`put_table_date` AS `puttable_dt`,`a`.`maturity_date` AS `mrty_dt`,`a`.`payment_date` AS `payment_dt`,`a`.`redeem_date` AS `redem_dt`,`a`.`delist_date` AS `delist_dt`,`a`.`pay_day` AS `pay_day`,`mp`.`dictionary_relation_code` AS `bond_type1_id`,`mp`.`dictionary_relation_code` AS `bond_type2_id`,`a`.`bond_form_id` AS `bond_form_id`,`a`.`other_nature` AS `other_nature`,`a`.`credit_rating` AS `credit_rating`,`a`.`issue_vol` AS `issue_vol`,`a`.`list_vol_sz` AS `listvolsz`,`a`.`list_vol_sh` AS `listvolsh`,`a`.`bond_period` AS `bond_period`,`a`.`par_value` AS `par_value`,`a`.`issue_price` AS `issue_price`,`a`.`coupon_type_code` AS `coupon_type_cd`,`a`.`pay_type_code` AS `pay_type_cd`,`a`.`pay_desc` AS `pay_desc`,`a`.`coupon_rule_code` AS `coupon_rule_cd`,`a`.`payment_type_code` AS `payment_type_cd`,`a`.`coupon_rate` AS `coupon_rate`,`a`.`floor_rate` AS `floor_rate`,`a`.`bnchmk_spread` AS `bnchmk_spread`,`a`.`bnchmk_id` AS `bnchmk_id`,`a`.`rate_desc` AS `rate_desc`,`a`.`pay_per_year` AS `pay_peryear`,`a`.`add_rate` AS `add_rate`,`a`.`put_table_price` AS `puttable_price`,`a`.`expect_rate` AS `expect_rate`,`a`.`issue_type_code` AS `issue_type_cd`,`a`.`reference_rate` AS `refe_rate`,`a`.`add_issue_num` AS `add_issue_num`,`a`.`is_cross` AS `is_cross`,`a`.`is_floor_rate` AS `is_floor_rate`,`a`.`is_adjust_type` AS `is_adjust_type`,`a`.`is_redeem` AS `is_redem`,`a`.`is_plit_debt` AS `is_plit_debt`,`a`.`is_put_table` AS `is_puttable`,`a`.`is_change` AS `is_change`,`a`.`fwd_rate` AS `fwd_rate`,`a`.`redeem_price` AS `redem_price`,`a`.`swaps_code` AS `swaps_cd`,`a`.`tax_rate` AS `tax_rate`,`a`.`coupon_style_code` AS `coupon_style_cd`,`a`.`option_termdes` AS `Option_Termdes`,`a`.`base_asset` AS `Base_Asset`,`a`.`coupon_method_code` AS `coupon_method_cd`,`a`.`bond_option` AS `bond_option`,`a`.`credit_type_in_id` AS `credit_typein_id`,`a`.`credit_type_out_id` AS `credit_typeout_id`,`a`.`remark` AS `remark`,`a`.`src_portfolio_cd` AS `src_portfolio_cd`,`a`.`delete_flag` AS `src_isdel`,ifnull(`a`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`a`.`delete_flag` AS `IS_DEL`,`a`.`create_dt` AS `CREATE_DT`,`a`.`updt_dt` AS `UPDT_DT`,(case when (ifnull(`mp`.`mapping_relation_code`,`mq`.`mapping_relation_code`) like '060007%') then 1 else 0 end) AS `is_abs`,`a`.`is_sub` AS `is_sub` from ((((((`cs_info_dw`.`dwd_bond_basic_info` `a` left join (select `n`.`dictionary_relation_code` AS `dictionary_relation_code`,`n`.`mapping_relation_code` AS `mapping_relation_code`,`n`.`source_code` AS `source_code`,`n`.`delete_flag` AS `delete_flag`,`n`.`dictionary_relation` AS `dictionary_relation` from (select `p`.`dictionary_relation_code` AS `dictionary_relation_code`,`p`.`mapping_relation_code` AS `mapping_relation_code`,`p`.`source_code` AS `source_code`,`p`.`delete_flag` AS `delete_flag`,`p`.`dictionary_relation` AS `dictionary_relation`,row_number() OVER (PARTITION BY `p`.`dictionary_relation_code` ORDER BY `p`.`dictionary_relation_id` )  AS `RN` from `cs_info_dw`.`dim_dictionary_relation` `p` where ((`p`.`dictionary_relation` = 'SECURITY_TYPE') and (`p`.`source_code` = 'CSCS'))) `n` where (`n`.`RN` = 1)) `mq` on(((`mq`.`dictionary_relation` = 'SECURITY_TYPE') and (`a`.`security_type_id` = `mq`.`dictionary_relation_code`) and (`mq`.`source_code` = 'CSCS') and (`mq`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `dlcc` on(((`a`.`security_type_id` = cast(`dlcc`.`constant_id` as char charset utf8mb4)) and (`dlcc`.`constant_type` = 201) and (`dlcc`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `mp` on(((`mp`.`dictionary_relation` = 'SECURITY_TYPE') and (`dlcc`.`constant_code` = `mp`.`mapping_relation_code`) and (`mp`.`source_code` = 'CSCS') and (`mp`.`delete_flag` = 0) and (((`a`.`issue_type_code` = 1) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010201)) or ((`a`.`issue_type_code` = 2) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010202)) or ((`dlcc`.`constant_code` <> '060005007') and (1 = 1)))))) left join `cs_info_dw`.`dim_dictionary_relation` `c2` on(((`a`.`currency` = `c2`.`mapping_relation_code`) and (`c2`.`dictionary_relation` = 'COMPY_BASICINFO.CURRENCY') and (`c2`.`source_code` = 'CSCS.MDS') and (`c2`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `c` on(((`c`.`constant_id` = `a`.`trade_market_id`) and (`c`.`constant_type` = 206) and (`c`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `c3` on(((`c`.`constant_code` = `c3`.`mapping_relation_code`) and (`c3`.`dictionary_relation` = 'MARKET_TYPE') and (`c3`.`source_code` = 'CSCS') and (`c3`.`delete_flag` = 0)))) where (`a`.`delete_flag` = 0)) `bond_basicinfo`", "source_tables": ["cs_info_dw.dwd_bond_basic_info", "cs_info_dw.dim_dictionary_relation", "cs_info_dw.dwd_lkp_char_code"], "table_count": 3, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_cbinfo", "view_definition": "select `t`.`pk_id` AS `BOND_CBINFO_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`swap_step` AS `SWAP_STEP`,`t`.`swap_str_date` AS `SWAP_STR_DT`,`t`.`swap_end_date` AS `SWAP_END_DT`,`t`.`call_str_date` AS `CALL_STR_DT`,`t`.`call_step` AS `CALL_STEP`,`t`.`trig_cnt_chg` AS `TRIG_CNT_CHG`,`t`.`is_put_sal_one` AS `ISPUT_SAL_ONE`,`t`.`add_price_fix_puts` AS `ADD_PRICE_FIX_PUTS`,`t`.`trig_meet_puts` AS `TRIG_MEET_PUTS`,`t`.`trig_meet_chg` AS `TRIG_MEET_CHG`,`t`.`trig_count_calls` AS `TRIG_COUNT_CALLS`,`t`.`change_frequency` AS `CHG_FRECY`,`t`.`puts_clause` AS `PUTS_CLAUSE`,`t`.`spchg` AS `SPCHG`,`t`.`call_sprice` AS `CALL_SPRICE`,`t`.`lday_not_calls` AS `LDAY_NOT_CALLS`,`t`.`is_spchg_div` AS `IS_SPCHG_DIV`,`t`.`right_alt` AS `RIGHT_ALT`,`t`.`share_deal` AS `SHARE_DEAL`,`t`.`is_inai_puts` AS `IS_INAI_PUTS`,`t`.`add_puts_price` AS `ADD_PUTS_PRICE`,`t`.`put_end_date` AS `PUT_END_DT`,`t`.`tax_deal` AS `TAX_DEAL`,`t`.`sp_chg_std` AS `SP_CHG_STD`,`t`.`maturity_calls_clause` AS `MATRY_CALLS_CLAUSE`,`t`.`floor_des_chg` AS `FLOOR_DES_CHG`,`t`.`add_puts_clause` AS `ADD_PUTS_CLAUSE`,`t`.`trig_count_puts` AS `TRIG_COUNT_PUTS`,`t`.`put_str_date` AS `PUT_STR_DT`,`t`.`maturity_calls_price` AS `MATRY_CALLS_PRICE`,`t`.`call_end_date` AS `CALL_END_DT`,`t`.`force_swap_clause` AS `FORCE_SWAP_CLAUSE`,`t`.`swap_rate` AS `SWAP_RATE`,`t`.`cbtd_fee` AS `CBTD_FEE`,`t`.`spfix_std` AS `SPFIX_STD`,`t`.`swap_price` AS `SWAP_PRICE`,`t`.`trig_meett_calls` AS `TRIG_MEETT_CALLS`,`t`.`remark` AS `REMARK`,`t`.`isin_aim_calls` AS `ISIN_AIM_CALLS`,`t`.`is_with_draw` AS `IS_WITH_DRAW`,`t`.`puts_day` AS `PUTS_DAY`,`t`.`trig_rate_calls` AS `TRIG_RATE_CALLS`,`t`.`is_chgs_price` AS `IS_CHGS_PRICE`,`t`.`is_inai_calls` AS `IS_INAI_CALLS`,`t`.`trig_rate_puts` AS `TRIG_RATE_PUTS`,`t`.`puts_price` AS `PUTS_PRICE`,`t`.`price_fix_puts` AS `PRICE_FIX_PUTS`,`t`.`calls_clause` AS `CALLS_CLAUSE`,`t`.`revise_clause` AS `REVISE_CLAUSE`,`t`.`trig_rate_chg` AS `TRIG_RATE_CHG`,`t`.`puts_frecy` AS `PUTS_FRECY`,`t`.`put_step` AS `PUT_STEP`,`t`.`calls_frecy` AS `CALLS_FRECY`,`t`.`price_fix_calls` AS `PRICE_FIX_CALLS`,`t`.`esourcememo` AS `ESOURCEMEMO`,NULL AS `SRC_PORTFOLIO_CD`,`t`.`delete_flag` AS `SRC_ISDEL`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_convertible_bond` `t`", "source_tables": ["cs_info_dw.dwd_bond_convertible_bond"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_cbuyredem", "view_definition": "select `t`.`pk_id` AS `BOND_CBUYREDEM_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`option_type_id` AS `OPTION_TYPE_ID`,`t`.`calls_type_id` AS `CALLS_TYPE_ID`,`t`.`start_date` AS `START_DT`,`t`.`end_date` AS `END_DT`,`t`.`reason_id` AS `REASON_ID`,`t`.`price` AS `PRICE`,`t`.`is_inai_id` AS `IS_INAI_ID`,`t`.`pub_date` AS `PUB_DT`,`t`.`pay_date` AS `PAY_DT`,`t`.`cash_date` AS `CASH_DT`,`t`.`recor_date` AS `RECOR_DT`,`t`.`remark` AS `REMARK`,`t`.`esourcememo` AS `ESOURCEMEMO`,NULL AS `SRC_PORTFOLIO_CD`,`t`.`delete_flag` AS `SRC_ISDEL`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_convertible_redeem` `t`", "source_tables": ["cs_info_dw.dwd_bond_convertible_redeem"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_chnbdyc", "view_definition": "select `t`.`pk_id` AS `BOND_CHNBDYC_SID`,`t`.`curve_code` AS `CURVE_CD`,`t`.`curve_name` AS `CURVE_NM`,`t`.`curve_type` AS `CURVE_TYPE`,`t`.`trade_date` AS `TRADE_DT`,`t`.`maturity` AS `MATURITY`,`t`.`yield` AS `YIELD`,`t`.`nyear` AS `NYEAR`,`t`.`kyear` AS `KYEAR`,`t`.`term_sn` AS `TERM_SN`,`t`.`bnchmk_rate` AS `BNCHMK_RATE`,`t`.`curve_rate` AS `CURVE_RATE`,NULL AS `SRC_UPDT_DT`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_chnbdyc` `t`", "source_tables": ["cs_info_dw.dwd_bond_chnbdyc"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_creditchg", "view_definition": "select `t`.`pk_id` AS `BOND_CREDITCHG_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`change_date` AS `CHANGE_DT`,`t`.`rating` AS `RATING`,`t`.`credit_id` AS `CREDIT_ID`,`t`.`credit_id` AS `company_global_id`,`b`.`company_name_cn` AS `CREDIT_NM`,`t`.`reason` AS `REASON`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`rating_type_code` AS `RATING_TYPE_CD`,`t`.`rating_style_code` AS `RATING_STYLE_CD`,`t`.`change_way_code` AS `CHANGE_WAY_CD`,`t`.`rating_fwd_code` AS `RATING_FWD_CD`,`t`.`remark` AS `REMARK`,`t`.`delete_flag` AS `ISDEL`,NULL AS `SRC_PORTFOLIO_CD`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_bond_credit_change` `t` left join `cs_info_dw`.`dwd_company_basic` `b` on(((`t`.`credit_id` = `b`.`company_global_id`) and (`b`.`delete_flag` = 0))))", "source_tables": ["cs_info_dw.dwd_bond_credit_change", "cs_info_dw.dwd_company_basic"], "table_count": 2, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_expectcash", "view_definition": "select `t`.`pk_id` AS `BOND_EXPECTCASH_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`start_date` AS `START_DT`,`t`.`end_date` AS `END_DT`,`t`.`cash_date` AS `CASH_DT`,`t`.`pay_start_date` AS `PAY_STARTDT`,`t`.`pay_end_date` AS `PAY_ENDDT`,`t`.`pay_other_date` AS `PAY_OTHERDT`,`t`.`right_date` AS `RIGHT_DT`,`t`.`register_date` AS `REGISTER_DT`,`t`.`pay_object` AS `PAY_OBJECT`,`t`.`coupon_rate` AS `COUPON_RATE`,`t`.`cash_ai` AS `CASH_AI`,`t`.`cash_prin` AS `CASH_PRIN`,`t`.`cash_prinai` AS `CASH_PRINAI`,`t`.`prin_vol` AS `PRIN_VOL`,`t`.`payment_vol` AS `PAYMENT_VOL`,`t`.`cash_vol` AS `CASH_VOL`,`t`.`pay_vol` AS `PAY_VOL`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_SECINNER_CD`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_expect_cash` `t`", "source_tables": ["cs_info_dw.dwd_bond_expect_cash"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_imprating_xw", "view_definition": "select `t`.`pk_id` AS `BOND_IMPRATING_XW_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`rating_date` AS `RATING_DATE`,`t`.`curve` AS `CURVE`,`a`.`security_short_name` AS `SECURITY_SNM`,`a`.`security_code` AS `SECURITY_FCD`,`t`.`curve_cd` AS `CURVE_CD`,`t`.`implied_rating` AS `IMPLIED_RATING`,`t`.`isin` AS `ISIN`,NULL AS `SRC_SID`,NULL AS `SRC_UPDT_DT`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_bond_imp_rating_xw` `t` join `cs_info_dw`.`dwd_security` `a` on(((`t`.`security_global_id` = `a`.`security_global_id`) and (`a`.`delete_flag` = 0))))", "source_tables": ["cs_info_dw.dwd_bond_imp_rating_xw", "cs_info_dw.dwd_security"], "table_count": 2, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_issue", "view_definition": "select `t`.`pk_id` AS `BOND_ISSUE_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`first_notice_date` AS `FIRST_NOTICE_DT`,`t`.`latest_update_date` AS `LATEST_UPDT_DT`,`t`.`issue_start_date` AS `ISSUE_STARTDT`,`t`.`issue_end_date` AS `ISSUE_ENDDT`,`t`.`issue_fee` AS `ISSUE_FEE`,`t`.`issue_object` AS `ISSUE_OBJECT`,`t`.`par_value` AS `PAR_VALUE`,`t`.`refe_rate` AS `REFE_RATE`,`t`.`issue_price` AS `ISSUE_PRICE`,`t`.`issue_state_id` AS `ISSUE_STATE_ID`,`t`.`issue_method_id` AS `ISSUE_METHOD_ID`,`t`.`issue_feerate` AS `ISSUE_FEERATE`,`t`.`pay_feerate` AS `PAY_FEERATE`,`t`.`issue_type_cd` AS `ISSUE_TYPE_CD`,`t`.`avg_period` AS `AVG_PERIOD`,`t`.`plan_maturity_date` AS `PLAN_MRTYDT`,`t`.`plan_issue_vol` AS `PLAN_ISSUEVOL`,`t`.`act_issue_vol` AS `ACT_ISSUEVOL`,`t`.`act_online_vol` AS `ACT_ONLINEVOL`,`t`.`act_offline_vol` AS `ACT_OFFLINEVOL`,`t`.`act_collect_val` AS `ACT_COLLECTVAL`,`t`.`total_issue_vol` AS `TOTAL_ISSUEVOL`,`t`.`agree_issue_vol` AS `AGREE_ISSUEVOL`,`t`.`udwrt_method_id` AS `UDWRT_METHOD_ID`,`t`.`udwrt_value` AS `UDWRT_VALUE`,`t`.`udwrt_fee` AS `UDWRT_FEE`,`t`.`udwrt_vol` AS `UDWRT_VOL`,`t`.`alluudwrt_vol` AS `ALLUUDWRT_VOL`,`t`.`low_buywal` AS `LOW_BUYWAL`,`t`.`add_buywal` AS `ADD_BUYWAL`,`t`.`bid_mark_id` AS `BID_MARK_ID`,`t`.`bid_method_id` AS `BID_METHOD_ID`,`t`.`bid_date` AS `BID_DATE`,`t`.`bid_floor` AS `BID_FLOOR`,`t`.`bid_limit` AS `BID_LIMIT`,`t`.`pay_date` AS `PAY_DT`,`t`.`addissue_num` AS `ADDISSUE_NUM`,`t`.`state_desc` AS `STATE_DESC`,`t`.`bfin_code` AS `BFIN_CD`,`t`.`addb_code` AS `ADDB_CD`,`t`.`register_id` AS `REGISTER_ID`,`t`.`remark` AS `REMARK`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_PORTFOLIO_CD`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_issue` `t`", "source_tables": ["cs_info_dw.dwd_bond_issue"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_ncconvrate", "view_definition": "select `t`.`pk_id` AS `BOND_NCCONVRATE_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`start_date` AS `START_DT`,`t`.`end_date` AS `END_DT`,`t`.`convert_rate` AS `CONV_RATE`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_SECINNER_CD`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_convert_rate` `t`", "source_tables": ["cs_info_dw.dwd_bond_convert_rate"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_opbuyredem", "view_definition": "select `t`.`pk_id` AS `BOND_OPBUYREDEM_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`update_date` AS `UPDATE_DT`,`t`.`security_code` AS `SECURITY_CD`,`t`.`trd_market_code` AS `TRD_MARKET_CD`,`t`.`option_type_id` AS `OPTION_TYPE_ID`,`t`.`num` AS `NUM`,`t`.`option_date` AS `OPTION_DT`,`t`.`start_date` AS `START_DT`,`t`.`end_date` AS `END_DT`,`t`.`price` AS `PRICE`,`t`.`isinai_id` AS `ISINAI_ID`,`t`.`interest` AS `INTEREST`,`t`.`low_val` AS `LOW_VAL`,`t`.`option_val` AS `OPTION_VAL`,`t`.`add_rate` AS `ADD_RATE`,`t`.`remark` AS `REMARK`,`t`.`option_type_id` AS `OPTIONDX_TYPE_ID`,`t`.`option_obj` AS `OPTION_OBJ`,`t`.`reasons_type` AS `REASONS_TYPE`,`t`.`esource_memo` AS `ESOURCE_MEMO`,`t`.`option_reason` AS `OPTION_REASON`,NULL AS `SRC_SECINNER_CD`,`t`.`delete_flag` AS `SRC_ISDEL`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_option_buy_redeem` `t`", "source_tables": ["cs_info_dw.dwd_bond_option_buy_redeem"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_opvolchg", "view_definition": "select `t`.`pk_id` AS `BOND_OPVOLCHG_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`latest_update_date` AS `LATEST_UPDT_DT`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`change_date` AS `CHANGE_DT`,`t`.`chg_type_id` AS `CHG_TYPE_ID`,`t`.`chg_vol` AS `CHG_VOL`,`t`.`remain_vol` AS `REMAIN_VOL`,`t`.`data_src` AS `DATA_SRC`,`t`.`remark` AS `REMARK`,`t`.`delete_flag` AS `SRC_ISDEL`,`b`.`src_portfolio_cd` AS `SRC_PORTFOLIO_CD`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_bond_vol_change` `t` join `cs_info_dw`.`dwd_bond_basic_info` `b` on(((`t`.`security_global_id` = `b`.`security_global_id`) and (`b`.`delete_flag` = 0))))", "source_tables": ["cs_info_dw.dwd_bond_vol_change", "cs_info_dw.dwd_bond_basic_info"], "table_count": 2, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_party", "view_definition": "select `t`.`pk_id` AS `BOND_PARTY_SID`,`t`.`party_company_code` AS `CSCS_COMPANY_CODE`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`party_id` AS `PARTY_ID`,`t`.`party_id` AS `company_global_id`,`t`.`party_name` AS `PARTY_NM`,`t`.`party_type_id` AS `PARTY_TYPE_ID`,`t`.`start_date` AS `START_DT`,`t`.`end_date` AS `END_DT`,`t`.`contact` AS `linkman`,`t`.`phone` AS `PHONE`,`t`.`fax` AS `FAX`,`t`.`delete_flag` AS `SRC_ISDEL`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_party` `t`", "source_tables": ["cs_info_dw.dwd_bond_party"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_payment_plus", "view_definition": "select `t`.`pk_id` AS `BOND_PAYMENT_PLUS_SID`,`t`.`security_global_id` AS `SECURITY_GLOBAL_ID`,`t`.`shortname` AS `SHORTNAME`,`t`.`bond_code` AS `SECURITY_CD`,`t`.`payday` AS `PAYMENT_DATE`,`t`.`payment_capital` AS `PAYMENT_CAPITAL`,`t`.`payment_interest` AS `PAYMENT_INTEREST`,`t`.`delete_flag` AS `SRC_ISDEL`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_payment_plus` `t`", "source_tables": ["cs_info_dw.dwd_bond_payment_plus"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_pledge", "view_definition": "select `s`.`BOND_PLEDGE_SID` AS `BOND_PLEDGE_SID`,`s`.`security_global_id` AS `security_global_id`,`s`.`SECINNER_ID` AS `SECINNER_ID`,`s`.`NOTICE_DT` AS `NOTICE_DT`,`s`.`PLEDGE_NM` AS `PLEDGE_NM`,`s`.`PLEDGE_TYPE_ID` AS `PLEDGE_TYPE_ID`,`s`.`PLEDGE_DESC` AS `PLEDGE_DESC`,`s`.`PLEDGE_OWNER_CODE` AS `PLEDGE_OWNER_CODE`,`s`.`PLEDGE_OWNER` AS `PLEDGE_OWNER`,`s`.`PLEDGE_VALUE` AS `PLEDGE_VALUE`,`s`.`PRIORITY_VALUE` AS `PRIORITY_VALUE`,`s`.`PLEDGE_DEPEND_ID` AS `PLEDGE_DEPEND_ID`,`s`.`PLEDGE_CONTROL_ID` AS `PLEDGE_CONTROL_ID`,`s`.`REGION` AS `REGION`,`s`.`MITIGATION_VALUE` AS `MITIGATION_VALUE`,`s`.`SRC_ISDEL` AS `SRC_ISDEL`,`s`.`SRC_CD` AS `SRC_CD`,`s`.`IS_DEL` AS `IS_DEL`,`s`.`CREATE_DT` AS `CREATE_DT`,`s`.`UPDT_DT` AS `UPDT_DT` from (select `d`.`BOND_PLEDGE_SID` AS `BOND_PLEDGE_SID`,`d`.`security_global_id` AS `security_global_id`,`d`.`SECINNER_ID` AS `SECINNER_ID`,`d`.`NOTICE_DT` AS `NOTICE_DT`,`d`.`PLEDGE_NM` AS `PLEDGE_NM`,`d`.`PLEDGE_TYPE_ID` AS `PLEDGE_TYPE_ID`,`d`.`PLEDGE_DESC` AS `PLEDGE_DESC`,`d`.`PLEDGE_OWNER_CODE` AS `PLEDGE_OWNER_CODE`,`d`.`PLEDGE_OWNER` AS `PLEDGE_OWNER`,`d`.`PLEDGE_VALUE` AS `PLEDGE_VALUE`,`d`.`PRIORITY_VALUE` AS `PRIORITY_VALUE`,`d`.`PLEDGE_DEPEND_ID` AS `PLEDGE_DEPEND_ID`,`d`.`PLEDGE_CONTROL_ID` AS `PLEDGE_CONTROL_ID`,`d`.`REGION` AS `REGION`,`d`.`MITIGATION_VALUE` AS `MITIGATION_VALUE`,`d`.`SRC_ISDEL` AS `SRC_ISDEL`,`d`.`SRC_CD` AS `SRC_CD`,`d`.`IS_DEL` AS `IS_DEL`,`d`.`CREATE_DT` AS `CREATE_DT`,`d`.`UPDT_DT` AS `UPDT_DT`,row_number() OVER (PARTITION BY `d`.`security_global_id`,`d`.`PLEDGE_NM` ORDER BY `d`.`UPDT_DT` desc,`d`.`CREATE_DT` desc )  AS `RN` from (select `t`.`pk_id` AS `BOND_PLEDGE_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`pledge_name` AS `PLEDGE_NM`,`t`.`pledge_type_id` AS `PLEDGE_TYPE_ID`,`t`.`pledge_desc` AS `PLEDGE_DESC`,`t`.`pledge_owner_code` AS `PLEDGE_OWNER_CODE`,`t`.`pledge_owner` AS `PLEDGE_OWNER`,`t`.`pledge_value` AS `PLEDGE_VALUE`,`t`.`priority_value` AS `PRIORITY_VALUE`,`t`.`pledge_depend_id` AS `PLEDGE_DEPEND_ID`,`t`.`pledge_control_id` AS `PLEDGE_CONTROL_ID`,`t`.`region` AS `REGION`,`t`.`mitigation_value` AS `MITIGATION_VALUE`,`t`.`delete_flag` AS `SRC_ISDEL`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_pledge` `t`) `d`) `s` where (`s`.`RN` = 1)", "source_tables": ["cs_info_dw.dwd_bond_pledge"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_pledge_cm", "view_definition": "select `bond_pledge`.`bond_pledge_sid` AS `bond_pledge_sid`,`bond_pledge`.`security_global_id` AS `security_global_id`,`bond_pledge`.`secinner_id` AS `secinner_id`,`bond_pledge`.`notice_dt` AS `notice_dt`,`bond_pledge`.`pledge_nm` AS `pledge_nm`,`bond_pledge`.`pledge_type_id` AS `pledge_type_id`,`bond_pledge`.`pledge_desc` AS `pledge_desc`,`bond_pledge`.`pledge_owner_code` AS `pledge_owner_code`,`bond_pledge`.`pledge_owner` AS `pledge_owner`,`bond_pledge`.`pledge_value` AS `pledge_value`,`bond_pledge`.`priority_value` AS `priority_value`,`bond_pledge`.`pledge_depend_id` AS `pledge_depend_id`,`bond_pledge`.`pledge_control_id` AS `pledge_control_id`,`bond_pledge`.`region` AS `region`,`bond_pledge`.`mitigation_value` AS `mitigation_value`,`bond_pledge`.`src_isdel` AS `src_isdel`,`bond_pledge`.`SRC_CD` AS `src_cd`,`bond_pledge`.`IS_DEL` AS `is_del`,`bond_pledge`.`CREATE_DT` AS `create_dt`,`bond_pledge`.`UPDT_DT` AS `updt_dt` from (select `t`.`pk_id` AS `bond_pledge_sid`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `secinner_id`,`t`.`notice_date` AS `notice_dt`,`t`.`pledge_name` AS `pledge_nm`,`t`.`pledge_type_id` AS `pledge_type_id`,`t`.`pledge_desc` AS `pledge_desc`,`t`.`pledge_owner_code` AS `pledge_owner_code`,`t`.`pledge_owner` AS `pledge_owner`,`t`.`pledge_value` AS `pledge_value`,`t`.`priority_value` AS `priority_value`,`t`.`pledge_depend_id` AS `pledge_depend_id`,`t`.`pledge_control_id` AS `pledge_control_id`,`t`.`region` AS `region`,`t`.`mitigation_value` AS `mitigation_value`,`t`.`delete_flag` AS `src_isdel`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_pledge` `t` where (`t`.`SOURCE_CODE` = 'MANUAL')) `bond_pledge`", "source_tables": ["cs_info_dw.dwd_bond_pledge"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_rating_yy_record", "view_definition": "select `t`.`pk_id` AS `RATING_YY_RECORD_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`yycurrentcoupon` AS `yycurrentcoupon`,`t`.`insert_date` AS `insert_date`,`t`.`pk_id` AS `source_id`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_rating_yy_record` `t`", "source_tables": ["cs_info_dw.dwd_bond_rating_yy_record"], "table_count": 1, "created_time": "2025-08-04T10:20:41.458340"}, {"view_name": "bond_redempprin", "view_definition": "select `t`.`pk_id` AS `BOND_REDEMPPRIN_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`pay_type_code` AS `PAY_TYPE_CD`,`t`.`payment_date` AS `PAYMENT_DT`,`t`.`pay_rate` AS `PAY_RATE`,`t`.`remain_rate` AS `REMAIN_RATE`,`t`.`remark` AS `REMARK`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_SECINNER_CD`,NULL AS `SRC_PORTFOLIO_CD`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_redeem_principal` `t`", "source_tables": ["cs_info_dw.dwd_bond_redeem_principal"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "bond_register", "view_definition": "select `t`.`pk_id` AS `BOND_REGISTER_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `company_id`,`t`.`emorder_id` AS `emorder_id`,`t`.`num` AS `num`,`t`.`issue_num` AS `issue_num`,`t`.`meet_date` AS `meet_date`,`t`.`fissue_date` AS `fissue_date`,`t`.`oreg_amount` AS `oreg_amount`,`t`.`general_amount` AS `general_amount`,`t`.`reg_amount` AS `reg_amount`,`t`.`amount_unit` AS `amount_unit`,`t`.`is_step` AS `is_step`,`t`.`notice_date` AS `notice_date`,`t`.`years` AS `years`,`t`.`security_type_code` AS `security_type_code`,`t`.`currency` AS `currency`,`t`.`reg_start_date` AS `reg_start_date`,`t`.`reg_end_date` AS `reg_end_date`,`t`.`remark` AS `remark`,`t`.`esourcememo` AS `esourcememo`,`t`.`updatedate` AS `updatedate`,`t`.`src_cd` AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_register` `t`", "source_tables": ["cs_info_dw.dwd_bond_register"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "bond_terms", "view_definition": "select `t`.`pk_id` AS `BOND_TERMS_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`latest_notice_date` AS `LATEST_NOTICE_DT`,`t`.`terms_type_id` AS `TERMS_TYPE_ID`,`t`.`terms` AS `TERMS`,`t`.`data_src` AS `DATA_SRC`,`t`.`remark` AS `REMARK`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_PORTFOLIO_CD`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_terms` `t`", "source_tables": ["cs_info_dw.dwd_bond_terms"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "bond_tradt", "view_definition": "select `t`.`pk_id` AS `BOND_TRADT_SID`,`t`.`type_code` AS `TYPE_CD`,ifnull(`c`.`dictionary_relation_code`,`t`.`trade_market_id`) AS `TRD_MARKET_ID`,`t`.`pp_date` AS `PP_DT`,`t`.`trade_date` AS `TRADE_DT`,`t`.`close_trade_pm` AS `CLOSE_TRADE_PM`,`t`.`open_trade_pm` AS `OPEN_TRADE_PM`,`t`.`esource_memo` AS `ESOURCE_MEMO`,`t`.`auction_oppm` AS `AUCTION_OPPM`,`t`.`close_trade_am` AS `CLOSE_TRADE_AM`,`t`.`open_trade_am` AS `OPEN_TRADE_AM`,`t`.`auction_copm` AS `AUCTION_COPM`,`t`.`auction_coam` AS `AUCTION_COAM`,`t`.`auction_opam` AS `AUCTION_OPAM`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRCID`,NULL AS `CREATE_BY`,NULL AS `UPDT_BY`,NULL AS `VERSION`,0 AS `STATUS`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from ((`cs_info_dw`.`dwd_bond_trade_date` `t` left join `cs_info_dw`.`dwd_lkp_char_code` `b` on(((`t`.`trade_market_id` = `b`.`constant_id`) and (`b`.`constant_type` = 206) and (`b`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `c` on(((`b`.`constant_code` = `c`.`mapping_relation_code`) and (`c`.`dictionary_relation` = 'MARKET_TYPE'))))", "source_tables": ["cs_info_dw.dwd_bond_trade_date", "cs_info_dw.dwd_lkp_char_code", "cs_info_dw.dim_dictionary_relation"], "table_count": 3, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "bond_valuations", "view_definition": "select `t`.`pk_id` AS `BOND_VALUATIONS_SID`,`t`.`security_global_id` AS `security_global_id`,`t`.`security_global_id` AS `SECINNER_ID`,`t`.`trade_date` AS `TRADE_DT`,`t`.`val_full` AS `VAL_FULL`,`t`.`ai` AS `AI`,`t`.`val_net` AS `VAL_NET`,`t`.`val_ytm` AS `VAL_YTM`,`t`.`val_mduration` AS `VAL_MDURATION`,`t`.`val_convexity` AS `VAL_CONVEXITY`,`t`.`val_bpvalue` AS `VAL_BPVALUE`,`t`.`val_spreadduration` AS `VAL_SPREADDURATION`,`t`.`val_spreadconvexity` AS `VAL_SPREADCONVEXITY`,`t`.`val_rateduration` AS `VAL_RATEDURATION`,`t`.`val_rateconvexity` AS `VAL_RATECONVEXITY`,`t`.`real_full` AS `REAL_FULL`,`t`.`real_net` AS `REAL_NET`,`t`.`real_ytm` AS `REAL_YTM`,`t`.`real_mduration` AS `REAL_MDURATION`,`t`.`real_convexity` AS `REAL_CONVEXITY`,`t`.`real_bpvalue` AS `REAL_BPVALUE`,`t`.`real_spreadduration` AS `REAL_SPREADDURATION`,`t`.`real_spreadconvexity` AS `REAL_SPREADCONVEXITY`,`t`.`real_rateduration` AS `REAL_RATEDURATION`,`t`.`real_rateconvexity` AS `REAL_RATECONVEXITY`,`t`.`remain_value` AS `REMAIN_VALUE`,`t`.`point_ytm` AS `POINT_YTM`,`t`.`val_fullfinal` AS `VAL_FULLFINAL`,`t`.`ai_finval` AS `AI_FINVAL`,`t`.`is_calculateoption` AS `IS_CALCULATEOPTION`,`t`.`datasrc_companyid` AS `DATASRC_COMPANYID`,`t`.`to_maturity` AS `TO_MATURITY`,`t`.`corres_yieldcurve` AS `CORRES_YIELDCURVE`,`t`.`coupon_rate` AS `COUPON_RATE`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_SECINNER_CD`,NULL AS `DATASRC_COMPANYCD`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_valuations` `t`", "source_tables": ["cs_info_dw.dwd_bond_valuations"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "bond_violation", "view_definition": "select `t`.`company_global_id` AS `company_global_id`,`t`.`security_global_id` AS `security_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`security_global_id` AS `SECINNER_ID`,`a`.`security_name` AS `SECURITY_NM`,`t`.`security_code` AS `SECURITY_CD`,`t`.`info_code` AS `INFO_CD`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`notice_title` AS `NOTICE_TITLE`,`t`.`detail` AS `DETAIL`,`t`.`delete_flag` AS `SRC_ISDEL`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`,`t`.`pk_id` AS `VIOLATION_SID`,`t`.`exposure` AS `EXPOSURE` from (`cs_info_dw`.`dwd_bond_violation` `t` join `cs_info_dw`.`dwd_security` `a` on(((`t`.`security_global_id` = `a`.`security_global_id`) and (`a`.`delete_flag` = 0))))", "source_tables": ["cs_info_dw.dwd_bond_violation", "cs_info_dw.dwd_security"], "table_count": 2, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "bond_warrantor", "view_definition": "select `s`.`bond_warrantor_sid` AS `BOND_WARRANTOR_SID`,`s`.`security_global_id` AS `security_global_id`,`s`.`secinner_id` AS `SECINNER_ID`,`s`.`notice_dt` AS `NOTICE_DT`,`s`.`warranty_rate` AS `WARRANTY_RATE`,`s`.`guarantee_type_id` AS `GUARANTEE_TYPE_ID`,`s`.`warranty_period` AS `WARRANTY_PERIOD`,`s`.`warrantor_id` AS `WARRANTOR_ID`,`s`.`company_global_id` AS `company_global_id`,`s`.`warrantor_nm` AS `WARRANTOR_NM`,`s`.`warrantor_type_id` AS `WARRANTOR_TYPE_ID`,`s`.`start_dt` AS `START_DT`,`s`.`end_dt` AS `END_DT`,`s`.`warranty_amt` AS `WARRANTY_AMT`,`s`.`warrantor_resume` AS `WARRANTOR_RESUME`,`s`.`warranty_contract` AS `WARRANTY_CONTRACT`,`s`.`warranty_benef` AS `WARRANTY_BENEF`,`s`.`warranty_content` AS `WARRANTY_CONTENT`,`s`.`warranty_type_id` AS `WARRANTY_TYPE_ID`,`s`.`warranty_claim` AS `WARRANTY_CLAIM`,`s`.`warranty_strength_id` AS `WARRANTY_STRENGTH_ID`,`s`.`pay_step` AS `PAY_STEP`,`s`.`warranty_fee` AS `WARRANTY_FEE`,`s`.`exempt_set` AS `EXEMPT_SET`,`s`.`warranty_obj` AS `WARRANTY_OBJ`,`s`.`isser_credit` AS `ISSER_CREDIT`,`s`.`mitigation_value` AS `MITIGATION_VALUE`,`s`.`src_cd` AS `SRC_CD`,`s`.`is_del` AS `IS_DEL`,`s`.`create_dt` AS `CREATE_DT`,`s`.`UPDT_DT` AS `UPDT_DT` from (select `d`.`bond_warrantor_sid` AS `bond_warrantor_sid`,`d`.`security_global_id` AS `security_global_id`,`d`.`secinner_id` AS `secinner_id`,`d`.`notice_dt` AS `notice_dt`,`d`.`warranty_rate` AS `warranty_rate`,`d`.`guarantee_type_id` AS `guarantee_type_id`,`d`.`warranty_period` AS `warranty_period`,`d`.`warrantor_id` AS `warrantor_id`,`d`.`company_global_id` AS `company_global_id`,`d`.`warrantor_nm` AS `warrantor_nm`,`d`.`warrantor_type_id` AS `warrantor_type_id`,`d`.`start_dt` AS `start_dt`,`d`.`end_dt` AS `end_dt`,`d`.`warranty_amt` AS `warranty_amt`,`d`.`warrantor_resume` AS `warrantor_resume`,`d`.`warranty_contract` AS `warranty_contract`,`d`.`warranty_benef` AS `warranty_benef`,`d`.`warranty_content` AS `warranty_content`,`d`.`warranty_type_id` AS `warranty_type_id`,`d`.`warranty_claim` AS `warranty_claim`,`d`.`warranty_strength_id` AS `warranty_strength_id`,`d`.`pay_step` AS `pay_step`,`d`.`warranty_fee` AS `warranty_fee`,`d`.`exempt_set` AS `exempt_set`,`d`.`warranty_obj` AS `warranty_obj`,`d`.`isser_credit` AS `isser_credit`,`d`.`mitigation_value` AS `mitigation_value`,`d`.`SRC_CD` AS `src_cd`,`d`.`IS_DEL` AS `is_del`,`d`.`CREATE_DT` AS `create_dt`,`d`.`UPDT_DT` AS `UPDT_DT`,row_number() OVER (PARTITION BY `d`.`security_global_id`,`d`.`company_global_id` ORDER BY `d`.`UPDT_DT` desc,`d`.`CREATE_DT` desc )  AS `RN` from (select `a`.`pk_id` AS `bond_warrantor_sid`,`a`.`security_global_id` AS `security_global_id`,`a`.`security_global_id` AS `secinner_id`,`a`.`notice_date` AS `notice_dt`,`a`.`warranty_rate` AS `warranty_rate`,`a`.`guarantee_type_id` AS `guarantee_type_id`,`a`.`warranty_period` AS `warranty_period`,`a`.`warrantor_id` AS `warrantor_id`,ifnull(`a`.`warrantor_id`,0) AS `company_global_id`,ifnull(`a`.`warrantor_name`,'担保人') AS `warrantor_nm`,`a`.`warrantor_type_id` AS `warrantor_type_id`,`a`.`start_date` AS `start_dt`,`a`.`end_date` AS `end_dt`,`a`.`warranty_amt` AS `warranty_amt`,`a`.`warrantor_resume` AS `warrantor_resume`,`a`.`warranty_contract` AS `warranty_contract`,`a`.`warranty_benef` AS `warranty_benef`,`a`.`warranty_content` AS `warranty_content`,`a`.`warranty_type_id` AS `warranty_type_id`,`a`.`warranty_claim` AS `warranty_claim`,`a`.`warranty_strength_id` AS `warranty_strength_id`,`a`.`pay_step` AS `pay_step`,`a`.`warranty_fee` AS `warranty_fee`,`a`.`exempt_set` AS `exempt_set`,`a`.`warranty_obj` AS `warranty_obj`,`a`.`isser_credit` AS `isser_credit`,`a`.`mitigation_value` AS `mitigation_value`,ifnull(`a`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`a`.`delete_flag` AS `IS_DEL`,`a`.`create_dt` AS `CREATE_DT`,`a`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_bond_warrantor` `a`) `d`) `s` where (`s`.`RN` = 1)", "source_tables": ["cs_info_dw.dwd_bond_warrantor"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "bond_warrantor_cm", "view_definition": "select `s`.`bond_warrantor_sid` AS `BOND_WARRANTOR_SID`,`s`.`security_global_id` AS `security_global_id`,`s`.`secinner_id` AS `SECINNER_ID`,`s`.`notice_dt` AS `NOTICE_DT`,`s`.`warranty_rate` AS `WARRANTY_RATE`,`s`.`guarantee_type_id` AS `GUARANTEE_TYPE_ID`,`s`.`warranty_period` AS `WARRANTY_PERIOD`,`s`.`warrantor_id` AS `WARRANTOR_ID`,`s`.`company_global_id` AS `company_global_id`,`s`.`warrantor_nm` AS `WARRANTOR_NM`,`s`.`warrantor_type_id` AS `WARRANTOR_TYPE_ID`,`s`.`start_dt` AS `START_DT`,`s`.`end_dt` AS `END_DT`,`s`.`warranty_amt` AS `WARRANTY_AMT`,`s`.`warrantor_resume` AS `WARRANTOR_RESUME`,`s`.`warranty_contract` AS `WARRANTY_CONTRACT`,`s`.`warranty_benef` AS `WARRANTY_BENEF`,`s`.`warranty_content` AS `WARRANTY_CONTENT`,`s`.`warranty_type_id` AS `WARRANTY_TYPE_ID`,`s`.`warranty_claim` AS `WARRANTY_CLAIM`,`s`.`warranty_strength_id` AS `WARRANTY_STRENGTH_ID`,`s`.`pay_step` AS `PAY_STEP`,`s`.`warranty_fee` AS `WARRANTY_FEE`,`s`.`exempt_set` AS `EXEMPT_SET`,`s`.`warranty_obj` AS `WARRANTY_OBJ`,`s`.`isser_credit` AS `ISSER_CREDIT`,`s`.`mitigation_value` AS `MITIGATION_VALUE`,`s`.`src_cd` AS `SRC_CD`,`s`.`is_del` AS `IS_DEL`,`s`.`create_dt` AS `CREATE_DT`,`s`.`UPDT_DT` AS `UPDT_DT` from (select `d`.`bond_warrantor_sid` AS `bond_warrantor_sid`,`d`.`security_global_id` AS `security_global_id`,`d`.`secinner_id` AS `secinner_id`,`d`.`notice_dt` AS `notice_dt`,`d`.`warranty_rate` AS `warranty_rate`,`d`.`guarantee_type_id` AS `guarantee_type_id`,`d`.`warranty_period` AS `warranty_period`,`d`.`warrantor_id` AS `warrantor_id`,`d`.`company_global_id` AS `company_global_id`,`d`.`warrantor_nm` AS `warrantor_nm`,`d`.`warrantor_type_id` AS `warrantor_type_id`,`d`.`start_dt` AS `start_dt`,`d`.`end_dt` AS `end_dt`,`d`.`warranty_amt` AS `warranty_amt`,`d`.`warrantor_resume` AS `warrantor_resume`,`d`.`warranty_contract` AS `warranty_contract`,`d`.`warranty_benef` AS `warranty_benef`,`d`.`warranty_content` AS `warranty_content`,`d`.`warranty_type_id` AS `warranty_type_id`,`d`.`warranty_claim` AS `warranty_claim`,`d`.`warranty_strength_id` AS `warranty_strength_id`,`d`.`pay_step` AS `pay_step`,`d`.`warranty_fee` AS `warranty_fee`,`d`.`exempt_set` AS `exempt_set`,`d`.`warranty_obj` AS `warranty_obj`,`d`.`isser_credit` AS `isser_credit`,`d`.`mitigation_value` AS `mitigation_value`,`d`.`SRC_CD` AS `src_cd`,`d`.`IS_DEL` AS `is_del`,`d`.`CREATE_DT` AS `create_dt`,`d`.`UPDT_DT` AS `UPDT_DT`,row_number() OVER (PARTITION BY `d`.`security_global_id`,`d`.`company_global_id` ORDER BY `d`.`UPDT_DT` desc,`d`.`CREATE_DT` desc )  AS `RN` from (select `a`.`pk_id` AS `bond_warrantor_sid`,`a`.`security_global_id` AS `security_global_id`,`a`.`security_global_id` AS `secinner_id`,`a`.`notice_date` AS `notice_dt`,`a`.`warranty_rate` AS `warranty_rate`,`a`.`guarantee_type_id` AS `guarantee_type_id`,`a`.`warranty_period` AS `warranty_period`,`a`.`warrantor_id` AS `warrantor_id`,ifnull(`a`.`warrantor_id`,0) AS `company_global_id`,ifnull(`bb`.`company_name_cn`,`a`.`warrantor_name`) AS `warrantor_nm`,`a`.`warrantor_type_id` AS `warrantor_type_id`,`a`.`start_date` AS `start_dt`,`a`.`end_date` AS `end_dt`,`a`.`warranty_amt` AS `warranty_amt`,`a`.`warrantor_resume` AS `warrantor_resume`,`a`.`warranty_contract` AS `warranty_contract`,`a`.`warranty_benef` AS `warranty_benef`,`a`.`warranty_content` AS `warranty_content`,`a`.`warranty_type_id` AS `warranty_type_id`,`a`.`warranty_claim` AS `warranty_claim`,`a`.`warranty_strength_id` AS `warranty_strength_id`,`a`.`pay_step` AS `pay_step`,`a`.`warranty_fee` AS `warranty_fee`,`a`.`exempt_set` AS `exempt_set`,`a`.`warranty_obj` AS `warranty_obj`,`a`.`isser_credit` AS `isser_credit`,`a`.`mitigation_value` AS `mitigation_value`,ifnull(`a`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`a`.`delete_flag` AS `IS_DEL`,`a`.`create_dt` AS `CREATE_DT`,`a`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_bond_warrantor` `a` left join `cs_info_dw`.`dwd_company_basic` `bb` on(((`a`.`warrantor_id` = `bb`.`company_global_id`) and (`bb`.`delete_flag` = 0)))) where (`a`.`SOURCE_CODE` = 'MANUAL')) `d`) `s` where (`s`.`RN` = 1)", "source_tables": ["cs_info_dw.dwd_bond_warrantor", "cs_info_dw.dwd_company_basic"], "table_count": 2, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "capital_top_ari_new", "view_definition": "select `cs_info_dw`.`dwd_capital_top_ari_new`.`pk_id` AS `CAPITAL_TOP_SID`,`cs_info_dw`.`dwd_capital_top_ari_new`.`company_id` AS `COMPANY_ID`,`cs_info_dw`.`dwd_capital_top_ari_new`.`company_nm` AS `COMPANY_NM`,`cs_info_dw`.`dwd_capital_top_ari_new`.`top_node_id` AS `TOP_NODE_ID`,`cs_info_dw`.`dwd_capital_top_ari_new`.`top_node_nm` AS `TOP_NODE_NM`,`cs_info_dw`.`dwd_capital_top_ari_new`.`label` AS `LABEL`,`cs_info_dw`.`dwd_capital_top_ari_new`.`is_final_top` AS `IS_FINAL_TOP`,`cs_info_dw`.`dwd_capital_top_ari_new`.`ratio` AS `RATIO`,`cs_info_dw`.`dwd_capital_top_ari_new`.`type` AS `TYPE`,`cs_info_dw`.`dwd_capital_top_ari_new`.`layer` AS `LAYER`,`cs_info_dw`.`dwd_capital_top_ari_new`.`path` AS `PATH`,`cs_info_dw`.`dwd_capital_top_ari_new`.`delete_flag` AS `IS_DEL`,`cs_info_dw`.`dwd_capital_top_ari_new`.`create_dt` AS `CREATE_DT`,`cs_info_dw`.`dwd_capital_top_ari_new`.`updt_dt` AS `UPDT_DT`,`cs_info_dw`.`dwd_capital_top_ari_new`.`control_ratio` AS `CONTROL_RATIO` from `cs_info_dw`.`dwd_capital_top_ari_new`", "source_tables": ["cs_info_dw.dwd_capital_top_ari_new"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "cfg_dict_mapping_rela", "view_definition": "select `t`.`dictionary_relation_id` AS `DICT_MAPPING_RELA_ID`,`t`.`dictionary_relation` AS `DICT_RELA_ID`,`t`.`dictionary_relation_name` AS `DICT_RELA_NAME`,`t`.`dictionary_relation_code` AS `SEC_R_CODE`,`t`.`mapping_relation_code` AS `MAPPING_R_CODE`,`t`.`order_num` AS `ORDER_NUM`,`t`.`dictionary_relation_rule` AS `DICT_RELA_RULE`,`t`.`remark` AS `REMARK`,`t`.`source_code` AS `SRC_CD`,`t`.`create_time` AS `UPDT_DT`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_time` AS `CREATE_DT` from `cs_info_dw`.`dim_dictionary_relation` `t` where (`t`.`dictionary_relation` is not null)", "source_tables": ["cs_info_dw.dim_dictionary_relation"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "cm_bond_basicinfo", "view_definition": "select `bond_basicinfo`.`security_global_id` AS `security_global_id`,`bond_basicinfo`.`secinner_id` AS `secinner_id`,`bond_basicinfo`.`security_cscs_code` AS `security_cscs_code`,`bond_basicinfo`.`company_global_id` AS `company_global_id`,`bond_basicinfo`.`company_id` AS `company_id`,`bond_basicinfo`.`security_cd` AS `security_cd`,`bond_basicinfo`.`security_nm` AS `security_nm`,`bond_basicinfo`.`security_snm` AS `security_snm`,`bond_basicinfo`.`spell` AS `spell`,`bond_basicinfo`.`security_type_id` AS `security_type_id`,`bond_basicinfo`.`issue_year` AS `issue_year`,`bond_basicinfo`.`issue_num` AS `issue_num`,`bond_basicinfo`.`currency` AS `currency`,`bond_basicinfo`.`trade_market_id` AS `trade_market_id`,`bond_basicinfo`.`notice_dt` AS `notice_dt`,`bond_basicinfo`.`src_create_dt` AS `src_create_dt`,`bond_basicinfo`.`public_dt` AS `public_dt`,`bond_basicinfo`.`public_announce_dt` AS `public_announce_dt`,`bond_basicinfo`.`issue_dt` AS `issue_dt`,`bond_basicinfo`.`frst_value_dt` AS `frst_value_dt`,`bond_basicinfo`.`last_value_dt` AS `last_value_dt`,`bond_basicinfo`.`puttable_dt` AS `puttable_dt`,`bond_basicinfo`.`mrty_dt` AS `mrty_dt`,`bond_basicinfo`.`payment_dt` AS `payment_dt`,`bond_basicinfo`.`redem_dt` AS `redem_dt`,`bond_basicinfo`.`delist_dt` AS `delist_dt`,`bond_basicinfo`.`pay_day` AS `pay_day`,`bond_basicinfo`.`bond_type1_id` AS `bond_type1_id`,`bond_basicinfo`.`bond_type2_id` AS `bond_type2_id`,`bond_basicinfo`.`bond_form_id` AS `bond_form_id`,`bond_basicinfo`.`other_nature` AS `other_nature`,`bond_basicinfo`.`credit_rating` AS `credit_rating`,`bond_basicinfo`.`issue_vol` AS `issue_vol`,`bond_basicinfo`.`listvolsz` AS `listvolsz`,`bond_basicinfo`.`listvolsh` AS `listvolsh`,`bond_basicinfo`.`bond_period` AS `bond_period`,`bond_basicinfo`.`par_value` AS `par_value`,`bond_basicinfo`.`issue_price` AS `issue_price`,`bond_basicinfo`.`coupon_type_cd` AS `coupon_type_cd`,`bond_basicinfo`.`pay_type_cd` AS `pay_type_cd`,`bond_basicinfo`.`pay_desc` AS `pay_desc`,`bond_basicinfo`.`coupon_rule_cd` AS `coupon_rule_cd`,`bond_basicinfo`.`payment_type_cd` AS `payment_type_cd`,`bond_basicinfo`.`coupon_rate` AS `coupon_rate`,`bond_basicinfo`.`floor_rate` AS `floor_rate`,`bond_basicinfo`.`bnchmk_spread` AS `bnchmk_spread`,`bond_basicinfo`.`bnchmk_id` AS `bnchmk_id`,`bond_basicinfo`.`rate_desc` AS `rate_desc`,`bond_basicinfo`.`pay_peryear` AS `pay_peryear`,`bond_basicinfo`.`add_rate` AS `add_rate`,`bond_basicinfo`.`puttable_price` AS `puttable_price`,`bond_basicinfo`.`expect_rate` AS `expect_rate`,`bond_basicinfo`.`issue_type_cd` AS `issue_type_cd`,`bond_basicinfo`.`refe_rate` AS `refe_rate`,`bond_basicinfo`.`add_issue_num` AS `add_issue_num`,`bond_basicinfo`.`is_cross` AS `is_cross`,`bond_basicinfo`.`is_floor_rate` AS `is_floor_rate`,`bond_basicinfo`.`is_adjust_type` AS `is_adjust_type`,`bond_basicinfo`.`is_redem` AS `is_redem`,`bond_basicinfo`.`is_plit_debt` AS `is_plit_debt`,`bond_basicinfo`.`is_puttable` AS `is_puttable`,`bond_basicinfo`.`is_change` AS `is_change`,`bond_basicinfo`.`fwd_rate` AS `fwd_rate`,`bond_basicinfo`.`redem_price` AS `redem_price`,`bond_basicinfo`.`swaps_cd` AS `swaps_cd`,`bond_basicinfo`.`tax_rate` AS `tax_rate`,`bond_basicinfo`.`coupon_style_cd` AS `coupon_style_cd`,`bond_basicinfo`.`Option_Termdes` AS `option_termdes`,`bond_basicinfo`.`Base_Asset` AS `base_asset`,`bond_basicinfo`.`coupon_method_cd` AS `coupon_method_cd`,`bond_basicinfo`.`bond_option` AS `bond_option`,`bond_basicinfo`.`credit_typein_id` AS `credit_typein_id`,`bond_basicinfo`.`credit_typeout_id` AS `credit_typeout_id`,`bond_basicinfo`.`remark` AS `remark`,`bond_basicinfo`.`src_portfolio_cd` AS `src_portfolio_cd`,`bond_basicinfo`.`src_isdel` AS `src_isdel`,`bond_basicinfo`.`SRC_CD` AS `src_cd`,`bond_basicinfo`.`is_del` AS `is_del`,`bond_basicinfo`.`create_dt` AS `create_dt`,`bond_basicinfo`.`UPDT_DT` AS `updt_dt`,`bond_basicinfo`.`is_abs` AS `is_abs`,`bond_basicinfo`.`is_sub` AS `is_sub` from (select `a`.`security_global_id` AS `security_global_id`,`a`.`security_global_id` AS `secinner_id`,`ds`.`security_cscs_code` AS `security_cscs_code`,`ds`.`company_global_id` AS `company_global_id`,`ds`.`company_global_id` AS `company_id`,`ds`.`security_code` AS `security_cd`,`ds`.`security_name` AS `security_nm`,`ds`.`security_short_name` AS `security_snm`,`ds`.`spell` AS `spell`,ifnull(`mp`.`dictionary_relation_code`,`a`.`security_type_id`) AS `security_type_id`,`a`.`issue_year` AS `issue_year`,`a`.`issue_num` AS `issue_num`,ifnull(`c2`.`dictionary_relation_code`,`a`.`currency`) AS `currency`,ifnull(`c3`.`dictionary_relation_code`,`a`.`trade_market_id`) AS `trade_market_id`,`a`.`notice_date` AS `notice_dt`,`a`.`create_date` AS `src_create_dt`,`a`.`public_date` AS `public_dt`,`a`.`public_announce_date` AS `public_announce_dt`,`a`.`issue_date` AS `issue_dt`,`a`.`first_value_date` AS `frst_value_dt`,`a`.`last_value_date` AS `last_value_dt`,`a`.`put_table_date` AS `puttable_dt`,`a`.`maturity_date` AS `mrty_dt`,`a`.`payment_date` AS `payment_dt`,`a`.`redeem_date` AS `redem_dt`,`a`.`delist_date` AS `delist_dt`,`a`.`pay_day` AS `pay_day`,`mp`.`dictionary_relation_code` AS `bond_type1_id`,`mp`.`dictionary_relation_code` AS `bond_type2_id`,`a`.`bond_form_id` AS `bond_form_id`,`a`.`other_nature` AS `other_nature`,`a`.`credit_rating` AS `credit_rating`,`a`.`issue_vol` AS `issue_vol`,`a`.`list_vol_sz` AS `listvolsz`,`a`.`list_vol_sh` AS `listvolsh`,`a`.`bond_period` AS `bond_period`,`a`.`par_value` AS `par_value`,`a`.`issue_price` AS `issue_price`,`a`.`coupon_type_code` AS `coupon_type_cd`,`a`.`pay_type_code` AS `pay_type_cd`,`a`.`pay_desc` AS `pay_desc`,`a`.`coupon_rule_code` AS `coupon_rule_cd`,`a`.`payment_type_code` AS `payment_type_cd`,`a`.`coupon_rate` AS `coupon_rate`,`a`.`floor_rate` AS `floor_rate`,`a`.`bnchmk_spread` AS `bnchmk_spread`,`a`.`bnchmk_id` AS `bnchmk_id`,`a`.`rate_desc` AS `rate_desc`,`a`.`pay_per_year` AS `pay_peryear`,`a`.`add_rate` AS `add_rate`,`a`.`put_table_price` AS `puttable_price`,`a`.`expect_rate` AS `expect_rate`,`a`.`issue_type_code` AS `issue_type_cd`,`a`.`reference_rate` AS `refe_rate`,`a`.`add_issue_num` AS `add_issue_num`,`a`.`is_cross` AS `is_cross`,`a`.`is_floor_rate` AS `is_floor_rate`,`a`.`is_adjust_type` AS `is_adjust_type`,`a`.`is_redeem` AS `is_redem`,`a`.`is_plit_debt` AS `is_plit_debt`,`a`.`is_put_table` AS `is_puttable`,`a`.`is_change` AS `is_change`,`a`.`fwd_rate` AS `fwd_rate`,`a`.`redeem_price` AS `redem_price`,`a`.`swaps_code` AS `swaps_cd`,`a`.`tax_rate` AS `tax_rate`,`a`.`coupon_style_code` AS `coupon_style_cd`,`a`.`option_termdes` AS `Option_Termdes`,`a`.`base_asset` AS `Base_Asset`,`a`.`coupon_method_code` AS `coupon_method_cd`,`a`.`bond_option` AS `bond_option`,`a`.`credit_type_in_id` AS `credit_typein_id`,`a`.`credit_type_out_id` AS `credit_typeout_id`,`a`.`remark` AS `remark`,`a`.`src_portfolio_cd` AS `src_portfolio_cd`,`a`.`IS_DEL` AS `src_isdel`,ifnull(`ds`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`a`.`IS_DEL` AS `is_del`,`a`.`CREATE_DT` AS `create_dt`,`a`.`UPDT_DT` AS `UPDT_DT`,(case when (`mp`.`mapping_relation_code` like '060007%') then 1 else 0 end) AS `is_abs`,`a`.`is_sub` AS `is_sub` from (((((((select `t`.`security_global_id` AS `security_global_id`,`t`.`issue_year` AS `issue_year`,`t`.`issue_num` AS `issue_num`,`t`.`currency` AS `currency`,`t`.`trade_market_id` AS `trade_market_id`,`t`.`notice_date` AS `notice_date`,`t`.`create_date` AS `create_date`,`t`.`public_date` AS `public_date`,`t`.`public_announce_date` AS `public_announce_date`,`t`.`issue_date` AS `issue_date`,`t`.`first_value_date` AS `first_value_date`,`t`.`last_value_date` AS `last_value_date`,`t`.`put_table_date` AS `put_table_date`,`t`.`maturity_date` AS `maturity_date`,`t`.`payment_date` AS `payment_date`,`t`.`redeem_date` AS `redeem_date`,`t`.`delist_date` AS `delist_date`,`t`.`pay_day` AS `pay_day`,`t`.`bond_type1_id` AS `bond_type1_id`,`t`.`bond_type2_id` AS `bond_type2_id`,`t`.`bond_form_id` AS `bond_form_id`,`t`.`other_nature` AS `other_nature`,`t`.`credit_rating` AS `credit_rating`,`t`.`issue_vol` AS `issue_vol`,`t`.`list_vol_sz` AS `list_vol_sz`,`t`.`list_vol_sh` AS `list_vol_sh`,`t`.`bond_period` AS `bond_period`,`t`.`par_value` AS `par_value`,`t`.`issue_price` AS `issue_price`,`t`.`coupon_type_code` AS `coupon_type_code`,`t`.`pay_type_code` AS `pay_type_code`,`t`.`pay_desc` AS `pay_desc`,`t`.`coupon_rule_code` AS `coupon_rule_code`,`t`.`payment_type_code` AS `payment_type_code`,`t`.`coupon_rate` AS `coupon_rate`,`t`.`floor_rate` AS `floor_rate`,`t`.`bnchmk_spread` AS `bnchmk_spread`,`t`.`bnchmk_id` AS `bnchmk_id`,`t`.`rate_desc` AS `rate_desc`,`t`.`pay_per_year` AS `pay_per_year`,`t`.`add_rate` AS `add_rate`,`t`.`put_table_price` AS `put_table_price`,`t`.`expect_rate` AS `expect_rate`,`t`.`issue_type_code` AS `issue_type_code`,`t`.`reference_rate` AS `reference_rate`,`t`.`add_issue_num` AS `add_issue_num`,`t`.`is_cross` AS `is_cross`,`t`.`is_floor_rate` AS `is_floor_rate`,`t`.`is_adjust_type` AS `is_adjust_type`,`t`.`is_redeem` AS `is_redeem`,`t`.`is_plit_debt` AS `is_plit_debt`,`t`.`is_put_table` AS `is_put_table`,`t`.`is_change` AS `is_change`,`t`.`fwd_rate` AS `fwd_rate`,`t`.`redeem_price` AS `redeem_price`,`t`.`swaps_code` AS `swaps_code`,`t`.`tax_rate` AS `tax_rate`,`t`.`coupon_style_code` AS `coupon_style_code`,`t`.`option_termdes` AS `option_termdes`,`t`.`base_asset` AS `base_asset`,`t`.`coupon_method_code` AS `coupon_method_code`,`t`.`bond_option` AS `bond_option`,`t`.`credit_type_in_id` AS `credit_type_in_id`,`t`.`credit_type_out_id` AS `credit_type_out_id`,`t`.`remark` AS `remark`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`,`t`.`security_type_id` AS `security_type_id`,`t`.`src_portfolio_cd` AS `src_portfolio_cd`,`t`.`is_sub` AS `is_sub`,`t`.`pay_order` AS `pay_order` from `cs_info_dw`.`dwd_bond_basic_info` `t`) `a` join `cs_info_dw`.`dwd_security` `ds` on(((`a`.`security_global_id` = `ds`.`security_global_id`) and (`ds`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `dlcc` on(((`a`.`security_type_id` = cast(`dlcc`.`constant_id` as char charset utf8mb4)) and (`dlcc`.`constant_type` = 201) and (`dlcc`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `mp` on(((`mp`.`dictionary_relation` = 'SECURITY_TYPE') and (`dlcc`.`constant_code` = `mp`.`mapping_relation_code`) and (`mp`.`source_code` = 'CSCS') and (`mp`.`delete_flag` = 0) and (((`a`.`issue_type_code` = 1) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010201)) or ((`a`.`issue_type_code` = 2) and (`dlcc`.`constant_code` = '060005007') and (`mp`.`dictionary_relation_code` = 20010202)) or ((`dlcc`.`constant_code` <> '060005007') and (1 = 1)))))) left join `cs_info_dw`.`dim_dictionary_relation` `c2` on(((`a`.`currency` = `c2`.`mapping_relation_code`) and (`c2`.`dictionary_relation` = 'COMPY_BASICINFO.CURRENCY') and (`c2`.`source_code` = 'CSCS.MDS') and (`c2`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_lkp_char_code` `c` on(((`c`.`constant_id` = `a`.`trade_market_id`) and (`c`.`constant_type` = 206) and (`c`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_relation` `c3` on(((`c`.`constant_code` = `c3`.`mapping_relation_code`) and (`c3`.`dictionary_relation` = 'MARKET_TYPE') and (`c3`.`source_code` = 'CSCS') and (`c3`.`delete_flag` = 0))))) `bond_basicinfo`", "source_tables": ["cs_info_dw.dwd_bond_basic_info", "cs_info_dw.dwd_security", "cs_info_dw.dwd_lkp_char_code", "cs_info_dw.dim_dictionary_relation"], "table_count": 4, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "cm_security", "view_definition": "select `s`.`security_global_id` AS `security_global_id`,`s`.`security_cscs_code` AS `security_cscs_code`,`s`.`security_code` AS `security_code`,`s`.`security_name` AS `security_name`,`s`.`security_short_name` AS `security_short_name`,`s`.`spell` AS `spell`,`s`.`security_type_id` AS `security_type_id`,`s`.`security_type` AS `security_type`,`s`.`trade_market_id` AS `trade_market_id`,`s`.`market_type` AS `market_type`,`s`.`company_code` AS `company_code`,ifnull(`a`.`party_global_id`,`s`.`company_global_id`) AS `company_global_id`,`s`.`list_status` AS `list_status`,`s`.`use_status` AS `use_status`,`s`.`currency` AS `currency`,`s`.`security_global_id` AS `source_id`,ifnull(`s`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`s`.`delete_flag` AS `delete_flag`,`s`.`create_dt` AS `create_dt`,`s`.`updt_dt` AS `UPDT_DT`,`s`.`list_date` AS `list_date`,`s`.`end_date` AS `end_date`,`b`.`record_scale` AS `record_scale`,`b`.`product_category` AS `product_category`,`b`.`operation_mode` AS `operation_mode`,`b`.`operation_status` AS `operation_status`,`b`.`risk_level` AS `risk_level`,(case when ((`b`.`end_date` is not null) and (length(trim(`b`.`end_date`)) > 0)) then replace(`b`.`end_date`,'-','') else `s`.`end_date` end) AS `mrty_dt`,`d`.`company_name_cn` AS `issuerName`,`d`.`organization_code` AS `issuerCreditCode`,substr(`d`.`organization_code`,9,9) AS `issuerOrganizationCode`,`c`.`category_code_1` AS `issuerCategoryCode1`,`c`.`category_code_2` AS `issuerCategoryCode2`,`c`.`category_code_3` AS `issuerCategoryCode3`,`s`.`company_global_id` AS `issuerGlobalId`,`d`.`organization_form` AS `issuerOrganizationFormId` from ((((`cs_info_dw`.`dwd_security` `s` left join `cs_info_dw`.`dwd_company_basic` `d` on(((`s`.`company_global_id` = `d`.`company_global_id`) and (`d`.`delete_flag` = 0)))) left join `cs_info_dw`.`dwd_company_basic_extend` `c` on(((`s`.`company_global_id` = `c`.`company_global_id`) and (`c`.`delete_flag` = 0)))) left join `cs_platform`.`manual_product` `b` on((`s`.`security_global_id` = `b`.`product_global_id`))) left join `cs_platform`.`manual_product_party` `a` on(((`a`.`product_id` = `b`.`product_id`) and (`a`.`party_type` = 'adminType'))))", "source_tables": ["cs_info_dw.dwd_security", "cs_info_dw.dwd_company_basic", "cs_info_dw.dwd_company_basic_extend", "cs_platform.manual_product", "cs_platform.manual_product_party"], "table_count": 5, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "company_affilparty", "view_definition": "select `t`.`pk_id` AS `COMPY_AFFILPARTY_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`report_date` AS `RPT_DT`,`t`.`affiliate_party_id` AS `AFFIL_PARTY_ID`,`t`.`affiliate_party_name` AS `AFFIL_PARTY`,`t`.`ohd_sha_ratio` AS `OHD_SHA_RATIO`,`t`.`ino_sha_ratio` AS `INO_SHA_RATIO`,ifnull(`b`.`dictionary_relation_code`,`t`.`relation_type`) AS `RELATION_TYPE_ID`,(case when (`t`.`affiliate_party_type` = 0) then '企业' when (`t`.`affiliate_party_type` = 1) then '个人' when (`t`.`affiliate_party_type` = 2) then '其他' else '未知' end) AS `AFFIL_PARTY_TYPE`,`t`.`is_combined` AS `IS_COMBINED`,`t`.`remark` AS `REMARK`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_COMPANY_CD`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_company_affiliate_party` `t` left join `cs_info_dw`.`dim_dictionary_relation` `b` on(((`t`.`relation_type` = `b`.`mapping_relation_code`) and (`b`.`dictionary_relation` = 'COMPY_AFFILPARTY.RELATION_TYPE_ID') and (`b`.`source_code` = 'CSCS.MDS'))))", "source_tables": ["cs_info_dw.dwd_company_affiliate_party", "cs_info_dw.dim_dictionary_relation"], "table_count": 2, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "company_balancesheet", "view_definition": "select `t`.`pk_id` AS `COMPY_BALANCESHEET_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`first_notice_date` AS `first_notice_date`,`t`.`latest_notice_date` AS `latest_notice_date`,cast(`t`.`report_date` as decimal(10,0)) AS `report_date`,cast(`t`.`start_date` as decimal(10,0)) AS `start_date`,cast(`t`.`end_date` as decimal(10,0)) AS `end_date`,`t`.`report_time_type_code` AS `report_time_type_code`,`t`.`combine_type_code` AS `combine_type_code`,`t`.`report_src_type_id` AS `report_src_type_id`,`t`.`data_adjust_type` AS `data_adjust_type`,`t`.`data_type` AS `data_type`,`t`.`is_public_report` AS `is_public_report`,`t`.`company_type` AS `company_type`,`t`.`currency` AS `currency`,`t`.`monetary_fund` AS `monetary_fund`,`t`.`tradef_asset` AS `tradef_asset`,`t`.`bill_rec` AS `bill_rec`,`t`.`account_rec` AS `account_rec`,`t`.`other_rec` AS `other_rec`,`t`.`advance_pay` AS `advance_pay`,`t`.`dividend_rec` AS `dividend_rec`,`t`.`interest_rec` AS `interest_rec`,`t`.`inventory` AS `inventory`,`t`.`nonl_asset_oneyear` AS `nonl_asset_oneyear`,`t`.`defer_expense` AS `defer_expense`,`t`.`other_lasset` AS `other_lasset`,`t`.`lasset_other` AS `lasset_other`,`t`.`lasset_balance` AS `lasset_balance`,`t`.`sum_lasset` AS `sum_lasset`,`t`.`saleable_fasset` AS `saleable_fasset`,`t`.`held_maturity_inv` AS `held_maturity_inv`,`t`.`estate_invest` AS `estate_invest`,`t`.`lte_quity_inv` AS `lte_quity_inv`,`t`.`ltrec` AS `ltrec`,`t`.`fixed_asset` AS `fixed_asset`,`t`.`construction_material` AS `construction_material`,`t`.`construction_progress` AS `construction_progress`,`t`.`liquidate_fixed_asset` AS `liquidate_fixed_asset`,`t`.`product_biology_asset` AS `product_biology_asset`,`t`.`oilgas_asset` AS `oilgas_asset`,`t`.`intangible_asset` AS `intangible_asset`,`t`.`develop_exp` AS `develop_exp`,`t`.`good_will` AS `good_will`,`t`.`ltdefer_asset` AS `ltdefer_asset`,`t`.`defer_incometax_asset` AS `defer_incometax_asset`,`t`.`other_nonl_asset` AS `other_nonl_asset`,`t`.`nonlasset_other` AS `nonlasset_other`,`t`.`nonlasset_balance` AS `nonlasset_balance`,`t`.`sum_nonl_asset` AS `sum_nonl_asset`,`t`.`cash_and_depositcbank` AS `cash_and_depositcbank`,`t`.`deposit_infi` AS `deposit_infi`,`t`.`fi_deposit` AS `fi_deposit`,`t`.`precious_metal` AS `precious_metal`,`t`.`lend_fund` AS `lend_fund`,`t`.`derive_fasset` AS `derive_fasset`,`t`.`buy_sellback_fasset` AS `buy_sellback_fasset`,`t`.`loan_advances` AS `loan_advances`,`t`.`agency_assets` AS `agency_assets`,`t`.`premium_rec` AS `premium_rec`,`t`.`subrogation_rec` AS `subrogation_rec`,`t`.`ri_rec` AS `ri_rec`,`t`.`undue_rireserve_rec` AS `undue_rireserve_rec`,`t`.`claim_rireserve_rec` AS `claim_rireserve_rec`,`t`.`life_rireserve_rec` AS `life_rireserve_rec`,`t`.`lthealth_rireserve_rec` AS `lthealth_rireserve_rec`,`t`.`gdeposit_pay` AS `gdeposit_pay`,`t`.`insured_pledge_loan` AS `insured_pledge_loan`,`t`.`capitalg_deposit_pay` AS `capitalg_deposit_pay`,`t`.`independent_asset` AS `independent_asset`,`t`.`client_fund` AS `client_fund`,`t`.`settlement_provision` AS `settlement_provision`,`t`.`client_provision` AS `client_provision`,`t`.`seat_fee` AS `seat_fee`,`t`.`other_asset` AS `other_asset`,`t`.`asset_other` AS `asset_other`,`t`.`asset_balance` AS `asset_balance`,`t`.`sum_asset` AS `sum_asset`,`t`.`st_borrow` AS `st_borrow`,`t`.`trade_fliab` AS `trade_fliab`,`t`.`bill_pay` AS `bill_pay`,`t`.`account_pay` AS `account_pay`,`t`.`advance_receive` AS `advance_receive`,`t`.`salary_pay` AS `salary_pay`,`t`.`tax_pay` AS `tax_pay`,`t`.`interest_pay` AS `interest_pay`,`t`.`dividend_pay` AS `dividend_pay`,`t`.`other_pay` AS `other_pay`,`t`.`accrue_expense` AS `accrue_expense`,`t`.`anticipate_liab` AS `anticipate_liab`,`t`.`defer_income` AS `defer_income`,`t`.`nonl_liab_oneyear` AS `nonl_liab_oneyear`,`t`.`other_lliab` AS `other_lliab`,`t`.`lliab_other` AS `lliab_other`,`t`.`lliab_balance` AS `lliab_balance`,`t`.`sum_lliab` AS `sum_lliab`,`t`.`lt_borrow` AS `lt_borrow`,`t`.`bond_pay` AS `bond_pay`,`t`.`lt_account_pay` AS `lt_account_pay`,`t`.`special_pay` AS `special_pay`,`t`.`defer_incometax_liab` AS `defer_incometax_liab`,`t`.`other_nonl_liab` AS `other_nonl_liab`,`t`.`nonl_liab_other` AS `nonl_liab_other`,`t`.`nonl_liab_balance` AS `nonl_liab_balance`,`t`.`sum_nonl_liab` AS `sum_nonl_liab`,`t`.`borrow_from_cbank` AS `borrow_from_cbank`,`t`.`borrow_fund` AS `borrow_fund`,`t`.`derive_financedebt` AS `derive_financedebt`,`t`.`sell_buyback_fasset` AS `sell_buyback_fasset`,`t`.`accept_deposit` AS `accept_deposit`,`t`.`agency_liab` AS `agency_liab`,`t`.`other_liab` AS `other_liab`,`t`.`premium_advance` AS `premium_advance`,`t`.`comm_pay` AS `comm_pay`,`t`.`ri_pay` AS `ri_pay`,`t`.`gdeposit_rec` AS `gdeposit_rec`,`t`.`insured_deposit_inv` AS `insured_deposit_inv`,`t`.`undue_reserve` AS `undue_reserve`,`t`.`claim_reserve` AS `claim_reserve`,`t`.`life_reserve` AS `life_reserve`,`t`.`lt_health_reserve` AS `lt_health_reserve`,`t`.`independent_liab` AS `independent_liab`,`t`.`pledge_borrow` AS `pledge_borrow`,`t`.`agent_trade_security` AS `agent_trade_security`,`t`.`agent_uw_security` AS `agent_uw_security`,`t`.`liab_other` AS `liab_other`,`t`.`liab_balance` AS `liab_balance`,`t`.`sum_liab` AS `sum_liab`,`t`.`share_capital` AS `share_capital`,`t`.`capital_reserve` AS `capital_reserve`,`t`.`surplus_reserve` AS `surplus_reserve`,`t`.`retained_earning` AS `retained_earning`,`t`.`inventory_share` AS `inventory_share`,`t`.`general_risk_prepare` AS `general_risk_prepare`,`t`.`diff_conversion_fc` AS `diff_conversion_fc`,`t`.`minority_equity` AS `minority_equity`,`t`.`sh_equity_other` AS `sh_equity_other`,`t`.`sh_equity_balance` AS `sh_equity_balance`,`t`.`sum_parent_equity` AS `sum_parent_equity`,`t`.`sum_sh_equity` AS `sum_sh_equity`,`t`.`liabsh_equity_other` AS `liabsh_equity_other`,`t`.`liabsh_equity_balance` AS `liabsh_equity_balance`,`t`.`sum_liabsh_equity` AS `sum_liabsh_equity`,`t`.`td_eposit` AS `td_eposit`,`t`.`st_bond_rec` AS `st_bond_rec`,`t`.`claim_pay` AS `claim_pay`,`t`.`policy_divi_pay` AS `policy_divi_pay`,`t`.`unconfirm_inv_loss` AS `unconfirm_inv_loss`,`t`.`ricontact_reserve_rec` AS `ricontact_reserve_rec`,`t`.`deposit` AS `deposit`,`t`.`contact_reserve` AS `contact_reserve`,`t`.`invest_rec` AS `invest_rec`,`t`.`specia_lreserve` AS `specia_lreserve`,`t`.`subsidy_rec` AS `subsidy_rec`,`t`.`marginout_fund` AS `marginout_fund`,`t`.`export_rebate_rec` AS `export_rebate_rec`,`t`.`defer_income_oneyear` AS `defer_income_oneyear`,`t`.`lt_salary_pay` AS `lt_salary_pay`,`t`.`fvalue_fasset` AS `fvalue_fasset`,`t`.`define_fvalue_fasset` AS `define_fvalue_fasset`,`t`.`internal_rec` AS `internal_rec`,`t`.`clheld_sale_ass` AS `clheld_sale_ass`,`t`.`fvalue_fliab` AS `fvalue_fliab`,`t`.`define_fvalue_fliab` AS `define_fvalue_fliab`,`t`.`internal_pay` AS `internal_pay`,`t`.`clheld_sale_liab` AS `clheld_sale_liab`,`t`.`anticipate_lliab` AS `anticipate_lliab`,`t`.`other_equity` AS `other_equity`,`t`.`other_cincome` AS `other_cincome`,`t`.`plan_cash_divi` AS `plan_cash_divi`,`t`.`parent_equity_other` AS `parent_equity_other`,`t`.`parent_equity_balance` AS `parent_equity_balance`,`t`.`preferred_stock` AS `preferred_stock`,`t`.`prefer_stoc_bond` AS `prefer_stoc_bond`,`t`.`cons_biolo_asset` AS `cons_biolo_asset`,`t`.`stock_num_end` AS `stock_num_end`,`t`.`net_mas_set` AS `net_mas_set`,`t`.`outward_remittance` AS `outward_remittance`,`t`.`cdandbill_rec` AS `cdandbill_rec`,`t`.`hedge_reserve` AS `hedge_reserve`,`t`.`suggest_assign_divi` AS `suggest_assign_divi`,`t`.`marginout_security` AS `marginout_security`,`t`.`cagent_trade_security` AS `cagent_trade_security`,`t`.`trade_risk_prepare` AS `trade_risk_prepare`,`t`.`creditor_planinv` AS `creditor_planinv`,`t`.`short_financing` AS `short_financing`,`t`.`receivables` AS `receivables`,`t`.`remark` AS `REMARK`,`t`.`chk_status` AS `CHK_STATUS`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_COMPANY_CD`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`,`t`.`total_other_rece` AS `TOTAL_OTHER_RECE`,`t`.`total_other_payable` AS `TOTAL_OTHER_PAYABLE`,`t`.`trade_finasset_notfvtpl` AS `TRADE_FINASSET_NOTFVTPL`,`t`.`trade_finliab_notfvtpl` AS `TRADE_FINLIAB_NOTFVTPL`,`t`.`finance_rece` AS `FINANCE_RECE`,`t`.`useright_asset` AS `USERIGHT_ASSET`,`t`.`lease_liab` AS `LEASE_LIAB`,`t`.`cred_inv` AS `CRED_INV`,`t`.`oth_credinv` AS `OTH_CREDINV`,`t`.`other_equityinv` AS `OTHER_EQUITYINV`,`t`.`held_saleass` AS `held_saleass`,`t`.`held_saleliab` AS `held_saleliab`,`t`.`fvalue_compfasset` AS `fvalue_compfasset`,`t`.`fvalue_compfasset_fld` AS `fvalue_compfasset_fld`,`t`.`amorcost_fasset` AS `amorcost_fasset`,`t`.`amorcost_fliab` AS `amorcost_fliab`,`t`.`contract_asset` AS `contract_asset`,`t`.`contract_liab` AS `contract_liab`,`t`.`amorcost_fasset_fld` AS `amorcost_fasset_fld`,`t`.`amorcost_fliab_fld` AS `amorcost_fliab_fld`,`t`.`account_bill_pay` AS `account_bill_pay`,`t`.`account_bill_rec` AS `account_bill_rec`,`t`.`other_nonfasset` AS `other_nonfasset`,`t`.`total_trade_finliab` AS `total_trade_finliab`,`t`.`total_trade_finasset` AS `total_trade_finasset` from `cs_info_dw`.`dwd_factor_balance_sheet` `t` where (`t`.`delete_flag` = 0)", "source_tables": ["cs_info_dw.dwd_factor_balance_sheet"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "company_bankaddfin", "view_definition": "select `t`.`pk_id` AS `COMPY_BANKADDFIN_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`notice_date` AS `NOTICE_DT`,cast(`t`.`report_date` as decimal(10,0)) AS `RPT_DT`,cast(`t`.`end_date` as decimal(10,0)) AS `END_DT`,`t`.`combine_type_code` AS `COMBINE_TYPE_CD`,`t`.`unit` AS `UNIT`,`t`.`currency` AS `CURRENCY`,`t`.`item_type_code` AS `ITEM_TYPECD`,`t`.`item_code` AS `ITEM_CD`,`t`.`amt_end` AS `AMT_END`,`t`.`amt_avg` AS `AMT_AVG`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_COMPANY_CD`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_company_bank_add_fin` `t`", "source_tables": ["cs_info_dw.dwd_company_bank_add_fin"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "company_basicinfo", "view_definition": "select `t`.`company_global_id` AS `COMPY_BASICINFO_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,lpad(`t`.`company_global_id`,15,0) AS `COMPANY_CD`,`t`.`company_name_cn` AS `COMPANY_NM`,`t`.`company_short_name` AS `COMPANY_SNM`,`t`.`cleans_company_name` AS `CLENS_COMPANY_NM`,`t`.`company_name_en` AS `FEN_NM`,`t`.`leg_represent` AS `LEG_REPRESENT`,`t`.`chairman` AS `CHAIRMAN`,`t`.`general_manager` AS `GMANAGER`,`t`.`board_secretary` AS `BSECRETARY`,`t`.`organization_form` AS `ORG_FORM_ID`,`t`.`found_date` AS `FOUND_DT`,`t`.`register_capital_currency` AS `CURRENCY`,`t`.`register_capital` AS `REG_CAPITAL`,`t`.`register_country` AS `COUNTRY`,`t`.`register_region` AS `REGION`,`t`.`register_city` AS `CITY`,`t`.`register_address` AS `REG_ADDR`,`t`.`company_address` AS `OFFICE_ADDR`,`t`.`company_zip_code` AS `OFFICE_POST_CD`,`t`.`company_phone` AS `COMPANY_PH`,`t`.`company_fax` AS `COMPANY_FAX`,`t`.`company_email` AS `COMPANY_EM`,`t`.`company_web` AS `COMPANY_WEB`,`t`.`business_scope` AS `BUSIN_SCOPE`,`t`.`main_business` AS `MAIN_BUSIN`,`t`.`employ_number` AS `EMPLOY_NUM`,`t`.`business_license_number` AS `BLNUMB`,`t`.`national_tax_number` AS `NTRNUM`,`t`.`local_tax_number` AS `LTRNUM`,`t`.`organization_code` AS `ORGNUM`,`t`.`register_date` AS `REG_DT`,`t`.`info_url` AS `INFO_URL`,`t`.`info_news` AS `INFO_NEWS`,`t`.`accounting_firm` AS `ACCOUNTING_FIRM`,`t`.`legal_advisor` AS `LEGAL_ADVISOR`,`t`.`company_state` AS `COMPANY_ST`,`t`.`company_profile` AS `COMPANY_PROFILE`,NULL AS `SRC_COMPANY_CD`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`,`t`.`is_core` AS `IS_CORE`,`t`.`actual_capital` AS `ACTUAL_CAPITAL`,`t`.`business_term_start` AS `START_DT`,`t`.`business_term_end` AS `END_DT`,ifnull(`a`.`dictionary_name`,`t`.`company_state`) AS `ORIG_COMPANY_ST`,ifnull(`b`.`dictionary_name`,`t`.`organization_form`) AS `ORIG_ORG_FORM`,`t`.`register_government` AS `REG_GOV`,`t`.`revoke_date` AS `REVOKE_DT`,NULL AS `SRC_UPDT_DT` from ((`cs_info_dw`.`dwd_company_basic` `t` left join `cs_info_dw`.`dim_dictionary_info` `a` on(((`a`.`dictionary_type` = 'COMPY_BASICINFO.COMPANY_ST') and (`t`.`company_state` = `a`.`dictionary_code`) and (`a`.`delete_flag` = 0)))) left join `cs_info_dw`.`dim_dictionary_info` `b` on(((`b`.`dictionary_type` = 'COMPY_ALL_INFO.ORG_FORM_ID') and (`t`.`organization_form` = `b`.`dictionary_code`) and (`b`.`delete_flag` = 0)))) where (`t`.`delete_flag` = 0)", "source_tables": ["cs_info_dw.dwd_company_basic", "cs_info_dw.dim_dictionary_info"], "table_count": 2, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "company_bondissuer", "view_definition": "select `t`.`COMPANY_GLOBAL_ID` AS `company_global_id`,`t`.`COMPANY_GL<PERSON><PERSON>L_ID` AS `COMPANY_ID`,`t`.`REGION` AS `REGION`,`t`.`ORG_NATURE_ID` AS `ORG_NATURE_ID`,`t`.`ACTUAL_CONTROL_SHARES_RATIO` AS `ACTCTRL_SHAREHD_RATIO`,`t`.`ORG_NATURE_ORIG` AS `ORG_NATURE_ORIG`,`t`.`DATA_SRC` AS `DATA_SRC`,`t`.`DELETE_FLAG` AS `SRC_ISDEL`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`DELETE_FLAG` AS `IS_DEL`,`t`.`CREATE_DT` AS `CREATE_DT`,`t`.`UPDT_DT` AS `UPDT_DT` from (select `cs_info_dw`.`dwd_company_bond_issuer`.`company_global_id` AS `COMPANY_GLOBAL_ID`,`cs_info_dw`.`dwd_company_bond_issuer`.`region` AS `REGION`,`cs_info_dw`.`dwd_company_bond_issuer`.`org_nature_id` AS `ORG_NATURE_ID`,`cs_info_dw`.`dwd_company_bond_issuer`.`actual_control_shares_ratio` AS `ACTUAL_CONTROL_SHARES_RATIO`,`cs_info_dw`.`dwd_company_bond_issuer`.`org_nature_orig` AS `ORG_NATURE_ORIG`,`cs_info_dw`.`dwd_company_bond_issuer`.`data_src` AS `DATA_SRC`,`cs_info_dw`.`dwd_company_bond_issuer`.`delete_flag` AS `DELETE_FLAG`,`cs_info_dw`.`dwd_company_bond_issuer`.`SOURCE_CODE` AS `SOURCE_CODE`,`cs_info_dw`.`dwd_company_bond_issuer`.`create_dt` AS `CREATE_DT`,`cs_info_dw`.`dwd_company_bond_issuer`.`updt_dt` AS `UPDT_DT`,row_number() OVER (PARTITION BY `cs_info_dw`.`dwd_company_bond_issuer`.`company_global_id` ORDER BY `cs_info_dw`.`dwd_company_bond_issuer`.`updt_dt` desc,`cs_info_dw`.`dwd_company_bond_issuer`.`create_dt` desc )  AS `RN` from `cs_info_dw`.`dwd_company_bond_issuer`) `t` where (`t`.`RN` = 1)", "source_tables": ["cs_info_dw.dwd_company_bond_issuer"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "company_cashflow", "view_definition": "select `t`.`pk_id` AS `COMPY_CASHFLOW_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`first_notice_date` AS `first_notice_date`,`t`.`latest_notice_date` AS `latest_notice_date`,cast(`t`.`report_date` as decimal(10,0)) AS `report_date`,cast(`t`.`start_date` as decimal(10,0)) AS `start_date`,cast(`t`.`end_date` as decimal(10,0)) AS `end_date`,`t`.`report_time_type_code` AS `report_time_type_code`,`t`.`combine_type_code` AS `combine_type_code`,`t`.`report_src_type_id` AS `report_src_type_id`,`t`.`data_adjust_type` AS `data_adjust_type`,`t`.`data_type` AS `data_type`,`t`.`is_public_report` AS `is_public_report`,`t`.`company_type` AS `company_type`,`t`.`currency` AS `currency`,`t`.`salegoods_service_rec` AS `salegoods_service_rec`,`t`.`tax_return_rec` AS `tax_return_rec`,`t`.`other_operate_rec` AS `other_operate_rec`,`t`.`ni_deposit` AS `ni_deposit`,`t`.`niborrow_from_cbank` AS `niborrow_from_cbank`,`t`.`niborrow_from_fi` AS `niborrow_from_fi`,`t`.`premium_rec` AS `premium_rec`,`t`.`nidisp_trade_fasset` AS `nidisp_trade_fasset`,`t`.`nidisp_saleable_fasset` AS `nidisp_saleable_fasset`,`t`.`niborrow_fund` AS `niborrow_fund`,`t`.`nibuyback_fund` AS `nibuyback_fund`,`t`.`operate_flowin_other` AS `operate_flowin_other`,`t`.`operate_flowin_balance` AS `operate_flowin_balance`,`t`.`sum_operate_flowin` AS `sum_operate_flowin`,`t`.`buygoods_service_pay` AS `buygoods_service_pay`,`t`.`employee_pay` AS `employee_pay`,`t`.`tax_pay` AS `tax_pay`,`t`.`other_operat_epay` AS `other_operat_epay`,`t`.`niloan_advances` AS `niloan_advances`,`t`.`nideposit_incbankfi` AS `nideposit_incbankfi`,`t`.`indemnity_pay` AS `indemnity_pay`,`t`.`intandcomm_pay` AS `intandcomm_pay`,`t`.`operate_flowout_other` AS `operate_flowout_other`,`t`.`operate_flowout_balance` AS `operate_flowout_balance`,`t`.`sum_operate_flowout` AS `sum_operate_flowout`,`t`.`operate_flow_other` AS `operate_flow_other`,`t`.`operate_flow_balance` AS `operate_flow_balance`,`t`.`net_operate_cashflow` AS `net_operate_cashflow`,`t`.`disposal_inv_rec` AS `disposal_inv_rec`,`t`.`inv_income_rec` AS `inv_income_rec`,`t`.`disp_filasset_rec` AS `disp_filasset_rec`,`t`.`disp_subsidiary_rec` AS `disp_subsidiary_rec`,`t`.`other_invrec` AS `other_invrec`,`t`.`inv_flowin_other` AS `inv_flowin_other`,`t`.`inv_flowin_balance` AS `inv_flowin_balance`,`t`.`sum_inv_flowin` AS `sum_inv_flowin`,`t`.`buy_filasset_pay` AS `buy_filasset_pay`,`t`.`inv_pay` AS `inv_pay`,`t`.`get_subsidiary_pay` AS `get_subsidiary_pay`,`t`.`other_inv_pay` AS `other_inv_pay`,`t`.`nipledge_loan` AS `nipledge_loan`,`t`.`inv_flowout_other` AS `inv_flowout_other`,`t`.`inv_flowout_balance` AS `inv_flowout_balance`,`t`.`sum_inv_flowout` AS `sum_inv_flowout`,`t`.`inv_flow_other` AS `inv_flow_other`,`t`.`inv_cashflow_balance` AS `inv_cashflow_balance`,`t`.`net_inv_cashflow` AS `net_inv_cashflow`,`t`.`accept_inv_rec` AS `accept_inv_rec`,`t`.`loan_rec` AS `loan_rec`,`t`.`other_fina_rec` AS `other_fina_rec`,`t`.`issue_bond_rec` AS `issue_bond_rec`,`t`.`niinsured_deposit_inv` AS `niinsured_deposit_inv`,`t`.`fina_flowin_other` AS `fina_flowin_other`,`t`.`fina_flowin_balance` AS `fina_flowin_balance`,`t`.`sum_fina_flowin` AS `sum_fina_flowin`,`t`.`repay_debt_pay` AS `repay_debt_pay`,`t`.`divi_profitorint_pay` AS `divi_profitorint_pay`,`t`.`other_fina_pay` AS `other_fina_pay`,`t`.`fina_flowout_other` AS `fina_flowout_other`,`t`.`fina_flowout_balance` AS `fina_flowout_balance`,`t`.`sum_fina_flowout` AS `sum_fina_flowout`,`t`.`fina_flow_other` AS `fina_flow_other`,`t`.`fina_flow_balance` AS `fina_flow_balance`,`t`.`net_fina_cashflow` AS `net_fina_cashflow`,`t`.`effect_exchange_rate` AS `effect_exchange_rate`,`t`.`nicash_equi_other` AS `nicash_equi_other`,`t`.`nicash_equi_balance` AS `nicash_equi_balance`,`t`.`nicash_equi` AS `nicash_equi`,`t`.`cash_equi_beginning` AS `cash_equi_beginning`,`t`.`cash_equi_ending` AS `cash_equi_ending`,`t`.`net_profit` AS `net_profit`,`t`.`asset_devalue` AS `asset_devalue`,`t`.`fixed_asset_etcdepr` AS `fixed_asset_etcdepr`,`t`.`intangible_asset_amor` AS `intangible_asset_amor`,`t`.`ltdefer_exp_amor` AS `ltdefer_exp_amor`,`t`.`defer_exp_reduce` AS `defer_exp_reduce`,`t`.`drawing_exp_add` AS `drawing_exp_add`,`t`.`disp_filasset_loss` AS `disp_filasset_loss`,`t`.`fixed_asset_loss` AS `fixed_asset_loss`,`t`.`fvalue_loss` AS `fvalue_loss`,`t`.`finance_exp` AS `finance_exp`,`t`.`inv_loss` AS `inv_loss`,`t`.`defer_taxasset_reduce` AS `defer_taxasset_reduce`,`t`.`defer_taxliab_add` AS `defer_taxliab_add`,`t`.`inventory_reduce` AS `inventory_reduce`,`t`.`operate_rec_reduce` AS `operate_rec_reduce`,`t`.`operate_pay_add` AS `operate_pay_add`,`t`.`inoperate_flow_other` AS `inoperate_flow_other`,`t`.`inoperate_flow_balance` AS `inoperate_flow_balance`,`t`.`innet_operate_cashflow` AS `innet_operate_cashflow`,`t`.`debt_to_capital` AS `debt_to_capital`,`t`.`cb_oneyear` AS `cb_oneyear`,`t`.`finalease_fixed_asset` AS `finalease_fixed_asset`,`t`.`cash_end` AS `cash_end`,`t`.`cash_begin` AS `cash_begin`,`t`.`equi_end` AS `equi_end`,`t`.`equi_begin` AS `equi_begin`,`t`.`innicash_equi_other` AS `innicash_equi_other`,`t`.`innicash_equi_balance` AS `innicash_equi_balance`,`t`.`innicash_equi` AS `innicash_equi`,`t`.`other` AS `other`,`t`.`subsidiary_accept` AS `subsidiary_accept`,`t`.`subsidiary_pay` AS `subsidiary_pay`,`t`.`divi_pay` AS `divi_pay`,`t`.`intandcomm_rec` AS `intandcomm_rec`,`t`.`net_rirec` AS `net_rirec`,`t`.`nilend_fund` AS `nilend_fund`,`t`.`defer_tax` AS `defer_tax`,`t`.`defer_income_amor` AS `defer_income_amor`,`t`.`exchange_loss` AS `exchange_loss`,`t`.`fixandestate_depr` AS `fixandestate_depr`,`t`.`fixed_asset_depr` AS `fixed_asset_depr`,`t`.`tradef_asset_reduce` AS `tradef_asset_reduce`,`t`.`ndloan_advances` AS `ndloan_advances`,`t`.`reduce_pledget_deposit` AS `reduce_pledget_deposit`,`t`.`add_pledget_deposit` AS `add_pledget_deposit`,`t`.`buy_subsidiary_pay` AS `buy_subsidiary_pay`,`t`.`cash_equiending_other` AS `cash_equiending_other`,`t`.`cash_equiending_balance` AS `cash_equiending_balance`,`t`.`nd_depositinc_bankfi` AS `nd_depositinc_bankfi`,`t`.`niborrow_sell_buyback` AS `niborrow_sell_buyback`,`t`.`ndlend_buy_sellback` AS `ndlend_buy_sellback`,`t`.`net_cd` AS `net_cd`,`t`.`nitrade_fliab` AS `nitrade_fliab`,`t`.`ndtrade_fasset` AS `ndtrade_fasset`,`t`.`disp_masset_rec` AS `disp_masset_rec`,`t`.`cancel_loan_rec` AS `cancel_loan_rec`,`t`.`ndborrow_from_cbank` AS `ndborrow_from_cbank`,`t`.`ndfide_posit` AS `ndfide_posit`,`t`.`ndissue_cd` AS `ndissue_cd`,`t`.`nilend_sell_buyback` AS `nilend_sell_buyback`,`t`.`ndborrow_sell_buyback` AS `ndborrow_sell_buyback`,`t`.`nitrade_fasset` AS `nitrade_fasset`,`t`.`ndtrade_fliab` AS `ndtrade_fliab`,`t`.`buy_finaleaseasset_pay` AS `buy_finaleaseasset_pay`,`t`.`niaccount_rec` AS `niaccount_rec`,`t`.`issue_cd` AS `issue_cd`,`t`.`addshare_capital_rec` AS `addshare_capital_rec`,`t`.`issue_share_rec` AS `issue_share_rec`,`t`.`bond_intpay` AS `bond_intpay`,`t`.`niother_finainstru` AS `niother_finainstru`,`t`.`agent_trade_securityrec` AS `agent_trade_securityrec`,`t`.`uwsecurity_rec` AS `uwsecurity_rec`,`t`.`buysellback_fasset_rec` AS `buysellback_fasset_rec`,`t`.`agent_uwsecurity_rec` AS `agent_uwsecurity_rec`,`t`.`nidirect_inv` AS `nidirect_inv`,`t`.`nitrade_settlement` AS `nitrade_settlement`,`t`.`buysellback_fasset_pay` AS `buysellback_fasset_pay`,`t`.`nddisp_trade_fasset` AS `nddisp_trade_fasset`,`t`.`ndother_fina_instr` AS `ndother_fina_instr`,`t`.`ndborrow_fund` AS `ndborrow_fund`,`t`.`nddirect_inv` AS `nddirect_inv`,`t`.`ndtrade_settlement` AS `ndtrade_settlement`,`t`.`ndbuyback_fund` AS `ndbuyback_fund`,`t`.`agenttrade_security_pay` AS `agenttrade_security_pay`,`t`.`nddisp_saleable_fasset` AS `nddisp_saleable_fasset`,`t`.`nisell_buyback` AS `nisell_buyback`,`t`.`ndbuy_sellback` AS `ndbuy_sellback`,`t`.`nettrade_fasset_rec` AS `nettrade_fasset_rec`,`t`.`net_ripay` AS `net_ripay`,`t`.`ndlend_fund` AS `ndlend_fund`,`t`.`nibuy_sellback` AS `nibuy_sellback`,`t`.`ndsell_buyback` AS `ndsell_buyback`,`t`.`ndinsured_deposit_inv` AS `ndinsured_deposit_inv`,`t`.`nettrade_fasset_pay` AS `nettrade_fasset_pay`,`t`.`niinsured_pledge_loan` AS `niinsured_pledge_loan`,`t`.`disp_subsidiary_pay` AS `disp_subsidiary_pay`,`t`.`netsell_buyback_fassetrec` AS `netsell_buyback_fassetrec`,`t`.`netsell_buyback_fassetpay` AS `netsell_buyback_fassetpay`,`t`.`remark` AS `REMARK`,`t`.`chk_status` AS `CHK_STATUS`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_COMPANY_CD`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`,`t`.`anticipate_liab_add` AS `anticipate_liab_add`,`t`.`norefercash_other` AS `norefercash_other` from `cs_info_dw`.`dwd_factor_cash_flow` `t` where (`t`.`delete_flag` = 0)", "source_tables": ["cs_info_dw.dwd_factor_cash_flow"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "company_category_xygs", "view_definition": "select `t`.`pk_id` AS `COMPANY_CATEGORY_XYGS_SID`,`a`.`company_code` AS `CSCS_CODE`,`a`.`company_global_id` AS `COMPANY_ID`,`t`.`category_code` AS `CATEGORY_CD`,`t`.`category_name_1` AS `CATE_1_NM`,`t`.`category_name_2` AS `CATE_2_NM`,`t`.`category_name_3` AS `CATE_3_NM`,`t`.`source_status` AS `SRC_STATUS`,NULL AS `SRC_COMPANY_CD`,NULL AS `SRC_ID`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,1100 AS `CREATE_BY`,`t`.`create_dt` AS `CREATE_DT`,1100 AS `UPDT_BY`,`t`.`create_dt` AS `UPDT_DT`,0 AS `VERSION`,0 AS `STATUS`,`a`.`company_global_id` AS `COMPANY_GLOBAL_ID` from (`cs_info_dw`.`dwd_company_category` `t` join `cs_info_dw`.`dwd_company_basic` `a` on(((`t`.`company_global_id` = `a`.`company_global_id`) and (`a`.`delete_flag` = 0))))", "source_tables": ["cs_info_dw.dwd_company_category", "cs_info_dw.dwd_company_basic"], "table_count": 2, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "company_chgnm", "view_definition": "select `t`.`pk_id` AS `COMPY_CHGNM_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`company_name_old` AS `COMPANY_NM_OLD`,`t`.`company_name_new` AS `COMPANY_NM_NEW`,`t`.`change_date` AS `CHANGE_DT`,NULL AS `SRC_COMPANY_CD`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_ID`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_company_change_name` `t`", "source_tables": ["cs_info_dw.dwd_company_change_name"], "table_count": 1, "created_time": "2025-08-04T10:20:41.459252"}, {"view_name": "company_company_industry", "view_definition": "select `t`.`pk_id` AS `COMPY_INDUSTRY_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,ifnull(`r`.`dictionary_relation_code`,`t`.`industry_sid`) AS `INDUSTRY_SID`,str_to_date(`t`.`start_date`,'%Y%m%d') AS `START_DT`,str_to_date(`t`.`end_date`,'%Y%m%d') AS `END_DT`,`t`.`is_new` AS `IS_NEW`,`t`.`delete_flag` AS `ISDEL`,NULL AS `SRC_COMPANY_CD`,10 AS `CLIENT_ID`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_company_industry` `t` join `cs_info_dw`.`dim_dictionary_relation` `r` on(((`t`.`industry_sid` = `r`.`mapping_relation_code`) and (`r`.`dictionary_relation` in ('INDUSTRY_SW_TYPE','INDUSTRY_ZJH2012_TYPE')))))", "source_tables": ["cs_info_dw.dwd_company_industry", "cs_info_dw.dim_dictionary_relation"], "table_count": 2, "created_time": "2025-08-04T10:20:41.460252"}, {"view_name": "company_core", "view_definition": "select `t`.`company_global_id` AS `COMPY_CORE_SID`,`t`.`company_global_id` AS `company_global_id`,`a`.`company_code` AS `cscs_code`,`t`.`company_global_id` AS `COMPANY_ID`,`a`.`company_name_cn` AS `COMPANY_NM`,`t`.`is_list` AS `IS_LIST`,`t`.`list_type` AS `LIST_TYPE`,`t`.`is_new_otc` AS `IS_NEW_OTC`,`t`.`is_bond` AS `IS_BOND`,`t`.`is_finance` AS `IS_FINANCE`,`t`.`finance_type` AS `FIN_TYPE`,`t`.`finance_type_subsidiary` AS `FIN_TYPE_SUPPV`,`t`.`is_it_finance` AS `IS_ITFIN`,`t`.`delete_flag` AS `SRC_ISDEL`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_company_core` `t` join `cs_info_dw`.`dwd_company_basic` `a` on(((`t`.`company_global_id` = `a`.`company_global_id`) and (`a`.`delete_flag` = 0))))", "source_tables": ["cs_info_dw.dwd_company_core", "cs_info_dw.dwd_company_basic"], "table_count": 2, "created_time": "2025-08-04T10:20:41.460252"}, {"view_name": "company_creditrating", "view_definition": "select `t`.`pk_id` AS `COMPY_CREDITRATING_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`type_code` AS `ITYPE_CD`,`t`.`rating_dt` AS `RATING_DT`,`t`.`rate_type_id` AS `RATE_TYPEID`,`t`.`rating` AS `RATING`,`t`.`rate_fwd_cd` AS `RATE_FWD_CD`,`t`.`credit_org_id` AS `credit_org_id`,`t`.`credit_org_id` AS `CREDIT_GLOBAL_ID`,`t`.`data_src_type` AS `DATASRC_TYPE`,`t`.`data_src` AS `DATA_SRC`,`t`.`remark` AS `REMARK`,`t`.`delete_flag` AS `SRC_ISDEL`,NULL AS `SRC_COMPANY_CD`,`t`.`src_url` AS `SRC_URL`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_company_credit_rating` `t`", "source_tables": ["cs_info_dw.dwd_company_credit_rating"], "table_count": 1, "created_time": "2025-08-04T10:20:41.460252"}, {"view_name": "company_creditrating_info", "view_definition": "select `t`.`pk_id` AS `COMPY_CREDITRATING_INFO_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`a`.`company_name_cn` AS `COMPANY_NM`,`t`.`credit_org_name` AS `CREDIT_ORG_NM`,`t`.`rating_current` AS `RATING_CURRENT`,`t`.`rating_prev` AS `RATING_PREV`,`t`.`rate_fwd_current` AS `RATE_FWD_CURRENT`,`t`.`rate_fwd_prev` AS `RATE_FWD_PREV`,`t`.`credit_rating` AS `CREDITRATING`,`t`.`rating_date_current` AS `RATING_DT_CURRENT`,`t`.`rating_date_prev` AS `RATING_DT_PREV`,`t`.`data_src` AS `DATA_SRC`,`t`.`data_src_content` AS `DATASRC_CONTENT`,`t`.`src_url` AS `SRC_URL`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_company_credit_rating_info` `t` join `cs_info_dw`.`dwd_company_basic` `a` on(((`t`.`company_global_id` = `a`.`company_global_id`) and (`a`.`delete_flag` = 0))))", "source_tables": ["cs_info_dw.dwd_company_credit_rating_info", "cs_info_dw.dwd_company_basic"], "table_count": 2, "created_time": "2025-08-04T10:20:41.460252"}, {"view_name": "company_finanaudit", "view_definition": "select `t`.`pk_id` AS `COMPY_FINANAUDIT_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`report_date` AS `RPT_DT`,`t`.`start_date` AS `START_DT`,`t`.`accounting_standards_type` AS `ACCTING_STRD_TYPECD`,`t`.`audit_view_type_id` AS `AUDIT_VIEW_TYPEID`,`t`.`audit_view` AS `AUDIT_VIEW`,`t`.`cpa` AS `CPA`,`t`.`audit_org` AS `AUDIT_ORG`,`t`.`audit_date` AS `AUDIT_DT`,`t`.`delete_flag` AS `SRC_ISDEL`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_company_finance_audit` `t`", "source_tables": ["cs_info_dw.dwd_company_finance_audit"], "table_count": 1, "created_time": "2025-08-04T10:20:41.460252"}, {"view_name": "company_idchg_map", "view_definition": "select `t`.`pk_id` AS `COMPY_IDCHG_MAP_SID`,`t`.`old_company_code` AS `OLD_COMPANY_CODE`,cast(`t`.`old_company_global_id` as char charset utf8mb4) AS `OLD_COMPANY_ID`,`t`.`new_company_code` AS `NEW_COMPANY_CODE`,cast(`t`.`new_company_global_id` as char charset utf8mb4) AS `NEW_COMPANY_ID`,`t`.`change_type` AS `CHANGE_TYPE`,`t`.`effective_start_dt` AS `EFFECTIVE_START_DT`,`t`.`effective_end_dt` AS `EFFECTIVE_END_DT`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from `cs_info_dw`.`dwd_company_id_change` `t`", "source_tables": ["cs_info_dw.dwd_company_id_change"], "table_count": 1, "created_time": "2025-08-04T10:20:41.460252"}, {"view_name": "company_incomestate", "view_definition": "select `t`.`pk_id` AS `COMPY_INCOMESTATE_SID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`first_notice_date` AS `FIRST_NOTICE_DT`,`t`.`latest_notice_date` AS `LATEST_NOTICE_DT`,cast(`t`.`report_date` as decimal(10,0)) AS `RPT_DT`,cast(`t`.`start_date` as decimal(10,0)) AS `START_DT`,cast(`t`.`end_date` as decimal(10,0)) AS `END_DT`,`t`.`report_time_type_code` AS `RPT_TIMETYPE_CD`,`t`.`combine_type_code` AS `COMBINE_TYPE_CD`,`t`.`report_src_type_id` AS `RPT_SRCTYPE_ID`,`t`.`data_adjust_type` AS `DATA_ADJUST_TYPE`,`t`.`data_type` AS `DATA_TYPE`,`t`.`is_public_report` AS `IS_PUBLIC_RPT`,`t`.`company_type` AS `COMPANY_TYPE`,`t`.`currency` AS `CURRENCY`,`t`.`operate_reve` AS `OPERATE_REVE`,`t`.`operate_exp` AS `OPERATE_EXP`,`t`.`operate_tax` AS `OPERATE_TAX`,`t`.`sale_exp` AS `SALE_EXP`,`t`.`manage_exp` AS `MANAGE_EXP`,`t`.`finance_exp` AS `FINANCE_EXP`,`t`.`asset_devalue_loss` AS `ASSET_DEVALUE_LOSS`,`t`.`fvalue_income` AS `FVALUE_INCOME`,`t`.`invest_income` AS `INVEST_INCOME`,`t`.`intn_reve` AS `INTN_REVE`,`t`.`int_reve` AS `INT_REVE`,`t`.`int_exp` AS `INT_EXP`,`t`.`commn_reve` AS `COMMN_REVE`,`t`.`comm_reve` AS `COMM_REVE`,`t`.`comm_exp` AS `COMM_EXP`,`t`.`exchange_income` AS `EXCHANGE_INCOME`,`t`.`premium_earned` AS `PREMIUM_EARNED`,`t`.`premium_income` AS `PREMIUM_INCOME`,`t`.`ripremium` AS `RIPREMIUM`,`t`.`undue_reserve` AS `UNDUE_RESERVE`,`t`.`premium_exp` AS `PREMIUM_EXP`,`t`.`indemnity_exp` AS `INDEMNITY_EXP`,`t`.`amortise_indemnity_exp` AS `AMORTISE_INDEMNITY_EXP`,`t`.`duty_reserve` AS `DUTY_RESERVE`,`t`.`amortise_duty_reserve` AS `AMORTISE_DUTY_RESERVE`,`t`.`rireve` AS `RIREVE`,`t`.`riexp` AS `RIEXP`,`t`.`surrender_premium` AS `SURRENDER_PREMIUM`,`t`.`policy_divi_exp` AS `POLICY_DIVI_EXP`,`t`.`amortise_riexp` AS `AMORTISE_RIEXP`,`t`.`agent_trade_security` AS `AGENT_TRADE_SECURITY`,`t`.`security_uw` AS `SECURITY_UW`,`t`.`client_asset_manage` AS `CLIENT_ASSET_MANAGE`,`t`.`operate_profit_other` AS `OPERATE_PROFIT_OTHER`,`t`.`operate_profit_balance` AS `OPERATE_PROFIT_BALANCE`,`t`.`operate_profit` AS `OPERATE_PROFIT`,`t`.`nonoperate_reve` AS `NONOPERATE_REVE`,`t`.`nonoperate_exp` AS `NONOPERATE_EXP`,`t`.`nonlasset_net_loss` AS `NONLASSET_NET_LOSS`,`t`.`sum_profit_other` AS `SUM_PROFIT_OTHER`,`t`.`sum_profit_balance` AS `SUM_PROFIT_BALANCE`,`t`.`sum_profit` AS `SUM_PROFIT`,`t`.`income_tax` AS `INCOME_TAX`,`t`.`net_profit_other2` AS `NET_PROFIT_OTHER2`,`t`.`net_profit_balance1` AS `NET_PROFIT_BALANCE1`,`t`.`net_profit_balance2` AS `NET_PROFIT_BALANCE2`,`t`.`net_profit` AS `NET_PROFIT`,`t`.`parent_net_profit` AS `PARENT_NET_PROFIT`,`t`.`minority_income` AS `MINORITY_INCOME`,`t`.`undistribute_profit` AS `UNDISTRIBUTE_PROFIT`,`t`.`basic_eps` AS `BASIC_EPS`,`t`.`diluted_eps` AS `DILUTED_EPS`,`t`.`invest_joint_income` AS `INVEST_JOINT_INCOME`,`t`.`total_operate_reve` AS `TOTAL_OPERATE_REVE`,`t`.`total_operate_exp` AS `TOTAL_OPERATE_EXP`,`t`.`other_reve` AS `OTHER_REVE`,`t`.`other_exp` AS `OTHER_EXP`,`t`.`unconfirm_invloss` AS `UNCONFIRM_INVLOSS`,`t`.`other_cincome` AS `OTHER_CINCOME`,`t`.`sum_cincome` AS `SUM_CINCOME`,`t`.`parent_cincome` AS `PARENT_CINCOME`,`t`.`minority_cincome` AS `MINORITY_CINCOME`,`t`.`net_contact_reserve` AS `NET_CONTACT_RESERVE`,`t`.`rdexp` AS `RDEXP`,`t`.`operate_manage_exp` AS `OPERATE_MANAGE_EXP`,`t`.`insur_reve` AS `INSUR_REVE`,`t`.`nonlasset_reve` AS `NONLASSET_REVE`,`t`.`total_operatereve_other` AS `TOTAL_OPERATEREVE_OTHER`,`t`.`net_indemnity_exp` AS `NET_INDEMNITY_EXP`,`t`.`total_operateexp_other` AS `TOTAL_OPERATEEXP_OTHER`,`t`.`net_profit_other1` AS `NET_PROFIT_OTHER1`,`t`.`cincome_balance1` AS `CINCOME_BALANCE1`,`t`.`cincome_balance2` AS `CINCOME_BALANCE2`,`t`.`other_net_income` AS `OTHER_NET_INCOME`,`t`.`reve_other` AS `REVE_OTHER`,`t`.`reve_balance` AS `REVE_BALANCE`,`t`.`operate_exp_other` AS `OPERATE_EXP_OTHER`,`t`.`operate_exp_balance` AS `OPERATE_EXP_BALANCE`,`t`.`bank_intnreve` AS `BANK_INTNREVE`,`t`.`bank_intreve` AS `BANK_INTREVE`,`t`.`ninsur_commn_reve` AS `NINSUR_COMMN_REVE`,`t`.`ninsur_comm_reve` AS `NINSUR_COMM_REVE`,`t`.`ninsur_comm_exp` AS `NINSUR_COMM_EXP`,`t`.`adisposal_income` AS `ADISPOSAL_INCOME`,`t`.`other_miincome` AS `OTHER_MIINCOME`,`t`.`remark` AS `REMARK`,`t`.`chk_status` AS `CHK_STATUS`,NULL AS `SRC_ISDEL`,NULL AS `SRC_COMPANY_CD`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT`,`t`.`ofwrd_exp` AS `ofwrd_exp`,`t`.`ofwint_exp` AS `ofwint_exp`,`t`.`ofwint_reve` AS `ofwint_reve`,`t`.`credit_impairment_income` AS `credit_impairment_income`,`t`.`minority_othercincome` AS `minority_othercincome`,`t`.`parent_othercincome` AS `parent_othercincome`,`t`.`net_exhedg_income` AS `net_exhedg_income` from `cs_info_dw`.`dwd_factor_income_state` `t` where (`t`.`delete_flag` = 0)", "source_tables": ["cs_info_dw.dwd_factor_income_state"], "table_count": 1, "created_time": "2025-08-04T10:20:41.460252"}, {"view_name": "company_insurerindex", "view_definition": "select `t`.`pk_id` AS `COMPY_INSURERINDEX_SID`,`t`.`company_global_id` AS `COMPANY_GLOBAL_ID`,`t`.`company_global_id` AS `COMPANY_ID`,`t`.`notice_date` AS `NOTICE_DT`,`t`.`report_date` AS `RPT_DT`,`t`.`unit` AS `UNIT`,`t`.`com_expend` AS `COM_EXPEND`,`t`.`act_capit_sx` AS `ACT_CAPIT_SX`,`t`.`com_compensate_cx` AS `COM_COMPENSATE_CX`,`t`.`act_capit_cx` AS `ACT_CAPIT_CX`,`t`.`inv_asset_cx` AS `INV_ASSET_CX`,`t`.`udr_reserve_sx` AS `UDR_RESERVE_SX`,`t`.`min_capit_sx` AS `MIN_CAPIT_SX`,`t`.`solven_ratio_sx` AS `SOLVEN_RATIO_SX`,`t`.`com_cost_cx` AS `COM_COST_CX`,`t`.`udr_reserve_cx` AS `UDR_RESERVE_CX`,`t`.`comexpend_cx` AS `COMEXPEND_CX`,`t`.`earnprem_gr_cx` AS `EARNPREM_GR_CX`,`t`.`earnprem_sx` AS `EARNPREM_SX`,`t`.`nrorsx` AS `NRORSX`,`t`.`nror` AS `NROR`,`t`.`tror` AS `TROR`,`t`.`min_capit` AS `MIN_CAPIT`,`t`.`act_capit` AS `ACT_CAPIT`,`t`.`inv_asset_sx` AS `INV_ASSET_SX`,`t`.`solven_ratio_cx` AS `SOLVEN_RATIO_CX`,`t`.`ostlr_cx` AS `OSTLR_CX`,`t`.`ror_cx` AS `ROR_CX`,`t`.`inv_asset` AS `INV_ASSET`,`t`.`earnprem_gr_sx` AS `EARNPREM_GR_SX`,`t`.`solven_ratio` AS `SOLVEN_RATIO`,`t`.`nror_cx` AS `NROR_CX`,`t`.`ostlr_sx` AS `OSTLR_SX`,`t`.`min_capit_cx` AS `MIN_CAPIT_CX`,`t`.`earnprem_gr` AS `EARNPREM_GR`,`t`.`earnprem_cx` AS `EARNPREM_CX`,`t`.`earnprem` AS `EARNPREM`,`t`.`sur_rate` AS `SUR_RATE`,`t`.`com_expend_sx` AS `COM_EXPEND_SX`,`t`.`ror_sx` AS `ROR_SX`,NULL AS `SRC_SRC_UPDT_DT`,NULL AS `SRC_ISDEL`,NULL AS `SRC_COMPANY_CD`,ifnull(`t`.`SOURCE_CODE`,'CSCS') AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_company_insurer_index` `t` left join `cs_info_dw`.`dwd_company_basic` `c` on((`t`.`company_global_id` = `c`.`company_global_id`)))", "source_tables": ["cs_info_dw.dwd_company_insurer_index", "cs_info_dw.dwd_company_basic"], "table_count": 2, "created_time": "2025-08-04T10:20:41.460252"}, {"view_name": "company_investment_xygs", "view_definition": "select `t`.`pk_id` AS `COMPANY_INVESTMENT_ID`,`t`.`company_global_id` AS `company_global_id`,`t`.`company_global_id` AS `COMPANY_GLOBAL_ID`,`t`.`report_date` AS `REPORT_DATE`,`b`.`business_license_number` AS `BUSINESS_LICENSE_NUMBER`,`t`.`invested_id` AS `INVESTED_ID`,`t`.`invested_name` AS `INVESTED_NAME`,`t`.`pledge_type` AS `PLEDGE_TYPE`,`t`.`pledge_ratio` AS `PLEDGE_RATIO`,`t`.`subscribed_capital_amount` AS `SUBSCRIBED_CAPITAL_AMOUNT`,`t`.`currency` AS `CURRENCY`,`b`.`register_capital` AS `REGISTER_CAPITAL`,`b`.`register_capital_currency` AS `REGISTER_CAPITAL_CURRENCY`,`b`.`company_state` AS `COMPANY_STATE`,`t`.`management_state` AS `MANAGEMENT_STATE`,`b`.`organization_code` AS `ORGANIZATION_CODE`,`b`.`organization_form` AS `ORGANIZATION_FORM`,`t`.`cancel_date` AS `CANCEL_DATE`,`b`.`found_date` AS `FOUND_DATE`,`b`.`revoke_date` AS `REVOKE_DATE`,`b`.`register_government` AS `REGISTER_GOVERNMENT`,NULL AS `SRC_IS_DEL`,NULL AS `NULL`,'CSCS' AS `SRC_CD`,`t`.`delete_flag` AS `IS_DEL`,`t`.`create_dt` AS `CREATE_DT`,`t`.`updt_dt` AS `UPDT_DT` from (`cs_info_dw`.`dwd_company_investment` `t` left join `cs_info_dw`.`dwd_company_basic` `b` on((`t`.`company_global_id` = `b`.`company_global_id`)))", "source_tables": ["cs_info_dw.dwd_company_investment", "cs_info_dw.dwd_company_basic"], "table_count": 2, "created_time": "2025-08-04T10:20:41.460252"}, {"view_name": "company_name_map", "view_definition": "select `t`.`pk_id` AS `company_name_map_sid`,`t`.`company_code` AS `cscs_code`,`t`.`company_global_id` AS `company_id`,`t1`.`company_name_cn` AS `company_nm`,`t`.`company_name_old` AS `company_nm_old`,`t`.`effective_date_start` AS `effective_start_dt`,`t`.`effective_date_end` AS `effective_end_dt`,'CSCS' AS `src_cd`,`t`.`delete_flag` AS `is_del`,1100 AS `create_by`,`t`.`create_dt` AS `create_dt`,1100 AS `updt_by`,`t`.`updt_dt` AS `updt_dt`,0 AS `version`,1 AS `status`,`t`.`company_global_id` AS `company_global_id` from (`cs_info_dw`.`dwd_company_name` `t` left join `cs_info_dw`.`dwd_company_basic` `t1` on(((`t`.`company_global_id` = `t1`.`company_global_id`) and (`t1`.`delete_flag` = 0))))", "source_tables": ["cs_info_dw.dwd_company_name", "cs_info_dw.dwd_company_basic"], "table_count": 2, "created_time": "2025-08-04T10:20:41.460252"}], "metadata": {"total_views": 50, "analyzed_views": 50, "total_tables": 51, "analysis_time": "2025-08-04T10:22:38.813909", "format_version": "2.0", "optimization": "batch_api_calls"}, "summary": {"total_views": 50, "analyzed_views": 50, "total_tables": 51, "analysis_time": "2025-08-04T10:22:38.814418"}}